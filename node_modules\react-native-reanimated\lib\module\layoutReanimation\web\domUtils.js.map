{"version": 3, "names": ["ReanimatedError", "logger", "isWindowAvailable", "setElementPosition", "snapshots", "Animations", "PREDEFINED_WEB_ANIMATIONS_ID", "CUSTOM_WEB_ANIMATIONS_ID", "animationNameToIndex", "Map", "animationNameList", "isObserverSet", "configureWebLayoutAnimations", "document", "getElementById", "predefinedAnimationsStyleTag", "createElement", "id", "onload", "sheet", "error", "animationName", "insertRule", "style", "customAnimationsStyleTag", "head", "append<PERSON><PERSON><PERSON>", "insertWebAnimation", "keyframe", "styleTag", "unshift", "set", "i", "length", "nextAnimationName", "nextAnimationIndex", "get", "undefined", "removeWebAnimation", "animationRemoveCallback", "currentAnimationIndex", "deleteRule", "splice", "delete", "timeoutScale", "frameDurationMs", "minimumFrames", "scheduleAnimationCleanup", "animationDuration", "timeoutValue", "Math", "max", "setTimeout", "reattachElementToAncestor", "child", "parent", "childSnapshot", "removedAfterAnimation", "originalOnAnimationEnd", "onanimationend", "event", "<PERSON><PERSON><PERSON><PERSON>", "call", "findDescendantWithExitingAnimation", "node", "root", "HTMLElement", "reanimatedDummy", "children", "Array", "from", "checkIfScreenWasChanged", "<PERSON><PERSON><PERSON><PERSON>", "reactFiberKey", "key", "Object", "keys", "startsWith", "memoizedProps", "navigation", "addHTMLMutationObserver", "observer", "MutationObserver", "mutationsList", "rootMutation", "target", "removedNodes", "observe", "body", "childList", "subtree", "areDOMRectsEqual", "r1", "r2", "x", "y", "width", "height"], "sourceRoot": "../../../../src", "sources": ["layoutReanimation/web/domUtils.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,eAAe,QAAQ,iBAAc;AAC9C,SAASC,MAAM,QAAQ,uBAAc;AACrC,SAASC,iBAAiB,QAAQ,0BAAuB;AAEzD,SAASC,kBAAkB,EAAEC,SAAS,QAAQ,qBAAkB;AAEhE,SAASC,UAAU,QAAQ,aAAU;AAErC,MAAMC,4BAA4B,GAAG,wCAAwC;AAC7E,MAAMC,wBAAwB,GAAG,oCAAoC;;AAErE;AACA,MAAMC,oBAAoB,GAAG,IAAIC,GAAG,CAAiB,CAAC;AACtD,MAAMC,iBAA2B,GAAG,EAAE;AAEtC,IAAIC,aAAa,GAAG,KAAK;;AAEzB;AACA;AACA;AACA;AACA,OAAO,SAASC,4BAA4BA,CAAA,EAAG;EAC7C,IACE,CAACV,iBAAiB,CAAC,CAAC;EAAI;EACxBW,QAAQ,CAACC,cAAc,CAACR,4BAA4B,CAAC,KAAK,IAAI,EAC9D;IACA;EACF;EAEA,MAAMS,4BAA4B,GAAGF,QAAQ,CAACG,aAAa,CAAC,OAAO,CAAC;EACpED,4BAA4B,CAACE,EAAE,GAAGX,4BAA4B;EAE9DS,4BAA4B,CAACG,MAAM,GAAG,MAAM;IAC1C,IAAI,CAACH,4BAA4B,CAACI,KAAK,EAAE;MACvClB,MAAM,CAACmB,KAAK,CAAC,gDAAgD,CAAC;MAC9D;IACF;IAEA,KAAK,MAAMC,aAAa,IAAIhB,UAAU,EAAE;MACtCU,4BAA4B,CAACI,KAAK,CAACG,UAAU,CAC3CjB,UAAU,CAACgB,aAAa,CAAmB,CAACE,KAC9C,CAAC;IACH;EACF,CAAC;EAED,MAAMC,wBAAwB,GAAGX,QAAQ,CAACG,aAAa,CAAC,OAAO,CAAC;EAChEQ,wBAAwB,CAACP,EAAE,GAAGV,wBAAwB;EAEtDM,QAAQ,CAACY,IAAI,CAACC,WAAW,CAACX,4BAA4B,CAAC;EACvDF,QAAQ,CAACY,IAAI,CAACC,WAAW,CAACF,wBAAwB,CAAC;AACrD;AAEA,OAAO,SAASG,kBAAkBA,CAACN,aAAqB,EAAEO,QAAgB,EAAE;EAC1E;EACA,IAAI,CAAC1B,iBAAiB,CAAC,CAAC,EAAE;IACxB;EACF;EAEA,MAAM2B,QAAQ,GAAGhB,QAAQ,CAACC,cAAc,CACtCP,wBACF,CAAqB;EAErB,IAAI,CAACsB,QAAQ,CAACV,KAAK,EAAE;IACnBlB,MAAM,CAACmB,KAAK,CAAC,gDAAgD,CAAC;IAC9D;EACF;EAEAS,QAAQ,CAACV,KAAK,CAACG,UAAU,CAACM,QAAQ,EAAE,CAAC,CAAC;EACtClB,iBAAiB,CAACoB,OAAO,CAACT,aAAa,CAAC;EACxCb,oBAAoB,CAACuB,GAAG,CAACV,aAAa,EAAE,CAAC,CAAC;EAE1C,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,iBAAiB,CAACuB,MAAM,EAAE,EAAED,CAAC,EAAE;IACjD,MAAME,iBAAiB,GAAGxB,iBAAiB,CAACsB,CAAC,CAAC;IAC9C,MAAMG,kBAAkB,GAAG3B,oBAAoB,CAAC4B,GAAG,CAACF,iBAAiB,CAAC;IAEtE,IAAIC,kBAAkB,KAAKE,SAAS,EAAE;MACpC,MAAM,IAAIrC,eAAe,CAAC,mCAAmC,CAAC;IAChE;IAEAQ,oBAAoB,CAACuB,GAAG,CAACrB,iBAAiB,CAACsB,CAAC,CAAC,EAAEG,kBAAkB,GAAG,CAAC,CAAC;EACxE;AACF;AAEA,SAASG,kBAAkBA,CACzBjB,aAAqB,EACrBkB,uBAAmC,EACnC;EACA;EACA,IAAI,CAACrC,iBAAiB,CAAC,CAAC,EAAE;IACxB;EACF;EAEA,MAAM2B,QAAQ,GAAGhB,QAAQ,CAACC,cAAc,CACtCP,wBACF,CAAqB;EAErB,MAAMiC,qBAAqB,GAAGhC,oBAAoB,CAAC4B,GAAG,CAACf,aAAa,CAAC;EAErE,IAAImB,qBAAqB,KAAKH,SAAS,EAAE;IACvC,MAAM,IAAIrC,eAAe,CAAC,mCAAmC,CAAC;EAChE;EAEAuC,uBAAuB,CAAC,CAAC;EAEzBV,QAAQ,CAACV,KAAK,EAAEsB,UAAU,CAACD,qBAAqB,CAAC;EAEjD9B,iBAAiB,CAACgC,MAAM,CAACF,qBAAqB,EAAE,CAAC,CAAC;EAClDhC,oBAAoB,CAACmC,MAAM,CAACtB,aAAa,CAAC;EAE1C,KAAK,IAAIW,CAAC,GAAGQ,qBAAqB,EAAER,CAAC,GAAGtB,iBAAiB,CAACuB,MAAM,EAAE,EAAED,CAAC,EAAE;IACrE,MAAME,iBAAiB,GAAGxB,iBAAiB,CAACsB,CAAC,CAAC;IAC9C,MAAMG,kBAAkB,GAAG3B,oBAAoB,CAAC4B,GAAG,CAACF,iBAAiB,CAAC;IAEtE,IAAIC,kBAAkB,KAAKE,SAAS,EAAE;MACpC,MAAM,IAAIrC,eAAe,CAAC,mCAAmC,CAAC;IAChE;IAEAQ,oBAAoB,CAACuB,GAAG,CAACrB,iBAAiB,CAACsB,CAAC,CAAC,EAAEG,kBAAkB,GAAG,CAAC,CAAC;EACxE;AACF;AAEA,MAAMS,YAAY,GAAG,IAAI,CAAC,CAAC;AAC3B,MAAMC,eAAe,GAAG,EAAE,CAAC,CAAC;AAC5B,MAAMC,aAAa,GAAG,EAAE;AAExB,OAAO,SAASC,wBAAwBA,CACtC1B,aAAqB,EACrB2B,iBAAyB,EACzBT,uBAAmC,EACnC;EACA;EACA;EACA,MAAMU,YAAY,GAAGC,IAAI,CAACC,GAAG,CAC3BH,iBAAiB,GAAGJ,YAAY,GAAG,IAAI,EACvCI,iBAAiB,GAAGH,eAAe,GAAGC,aACxC,CAAC;EAEDM,UAAU,CACR,MAAMd,kBAAkB,CAACjB,aAAa,EAAEkB,uBAAuB,CAAC,EAChEU,YACF,CAAC;AACH;AAEA,SAASI,yBAAyBA,CAACC,KAA4B,EAAEC,MAAY,EAAE;EAC7E,MAAMC,aAAa,GAAGpD,SAAS,CAACgC,GAAG,CAACkB,KAAK,CAAC;EAE1C,IAAI,CAACE,aAAa,EAAE;IAClBvD,MAAM,CAACmB,KAAK,CAAC,4BAA4B,CAAC;IAC1C;EACF;;EAEA;EACAkC,KAAK,CAACG,qBAAqB,GAAG,IAAI;EAClCF,MAAM,CAAC7B,WAAW,CAAC4B,KAAK,CAAC;EAEzBnD,kBAAkB,CAACmD,KAAK,EAAEE,aAAa,CAAC;EAExC,MAAME,sBAAsB,GAAGJ,KAAK,CAACK,cAAc;EAEnDL,KAAK,CAACK,cAAc,GAAG,UAAUC,KAAqB,EAAE;IACtDL,MAAM,CAACM,WAAW,CAACP,KAAK,CAAC;;IAEzB;IACAI,sBAAsB,EAAEI,IAAI,CAAC,IAAI,EAAEF,KAAK,CAAC;EAC3C,CAAC;AACH;AAEA,SAASG,kCAAkCA,CACzCC,IAA2B,EAC3BC,IAAU,EACV;EACA;EACA;EACA,IAAI,EAAED,IAAI,YAAYE,WAAW,CAAC,EAAE;IAClC;EACF;EAEA,IAAIF,IAAI,CAACG,eAAe,IAAIH,IAAI,CAACP,qBAAqB,KAAKpB,SAAS,EAAE;IACpEgB,yBAAyB,CAACW,IAAI,EAAEC,IAAI,CAAC;EACvC;EAEA,MAAMG,QAAQ,GAAGC,KAAK,CAACC,IAAI,CAACN,IAAI,CAACI,QAAQ,CAAC;EAE1C,KAAK,IAAIpC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoC,QAAQ,CAACnC,MAAM,EAAE,EAAED,CAAC,EAAE;IACxC+B,kCAAkC,CAChCK,QAAQ,CAACpC,CAAC,CAAC,EACXiC,IACF,CAAC;EACH;AACF;AAkBA,SAASM,uBAAuBA,CAC9BC,cAA0D,EAC1D;EACA,IAAIC,aAA2B,GAAG,cAAc;EAEhD,KAAK,MAAMC,GAAG,IAAIC,MAAM,CAACC,IAAI,CAACJ,cAAc,CAAC,EAAE;IAC7C,IAAIE,GAAG,CAACG,UAAU,CAAC,cAAc,CAAC,EAAE;MAClCJ,aAAa,GAAGC,GAAmB;MACnC;IACF;EACF;EAEA,OACEF,cAAc,CAACC,aAAa,CAAC,EAAEnB,KAAK,EAAEwB,aAAa,EAAEC,UAAU,KAC/D1C,SAAS;AAEb;AAEA,OAAO,SAAS2C,uBAAuBA,CAAA,EAAG;EACxC,IAAIrE,aAAa,IAAI,CAACT,iBAAiB,CAAC,CAAC,EAAE;IACzC;EACF;EAEAS,aAAa,GAAG,IAAI;EAEpB,MAAMsE,QAAQ,GAAG,IAAIC,gBAAgB,CAAEC,aAAa,IAAK;IACvD,MAAMC,YAAY,GAAGD,aAAa,CAACA,aAAa,CAAClD,MAAM,GAAG,CAAC,CAAC;IAE5D,IACEsC,uBAAuB,CACrBa,YAAY,CAACC,MACf,CAAC,EACD;MACA;IACF;IAEA,KAAK,IAAIrD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoD,YAAY,CAACE,YAAY,CAACrD,MAAM,EAAE,EAAED,CAAC,EAAE;MACzD+B,kCAAkC,CAChCqB,YAAY,CAACE,YAAY,CAACtD,CAAC,CAAC,EAC5BoD,YAAY,CAACC,MACf,CAAC;IACH;EACF,CAAC,CAAC;EAEFJ,QAAQ,CAACM,OAAO,CAAC1E,QAAQ,CAAC2E,IAAI,EAAE;IAAEC,SAAS,EAAE,IAAI;IAAEC,OAAO,EAAE;EAAK,CAAC,CAAC;AACrE;AAEA,OAAO,SAASC,gBAAgBA,CAACC,EAAW,EAAEC,EAAW,EAAE;EACzD;EACA,OACED,EAAE,CAACE,CAAC,KAAKD,EAAE,CAACC,CAAC,IACbF,EAAE,CAACG,CAAC,KAAKF,EAAE,CAACE,CAAC,IACbH,EAAE,CAACI,KAAK,KAAKH,EAAE,CAACG,KAAK,IACrBJ,EAAE,CAACK,MAAM,KAAKJ,EAAE,CAACI,MAAM;AAE3B", "ignoreList": []}