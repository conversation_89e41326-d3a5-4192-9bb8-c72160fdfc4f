{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "ScreenContext", "InnerScreen", "_react", "_interopRequireDefault", "require", "_reactNative", "_TransitionProgressContext", "_DelayedFreeze", "_core", "_ScreenNativeComponent", "_ModalScreenNativeComponent", "_usePrevious", "e", "__esModule", "_extends", "assign", "bind", "n", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "AnimatedNativeScreen", "Animated", "createAnimatedComponent", "ScreenNativeComponent", "AnimatedNativeModalScreen", "ModalScreenNativeComponent", "SHEET_FIT_TO_CONTENTS", "SHEET_COMPAT_LARGE", "SHEET_COMPAT_MEDIUM", "SHEET_COMPAT_ALL", "SHEET_DIMMED_ALWAYS", "assertDetentsArrayIsSorted", "array", "i", "Error", "resolveSheetAllowedDetents", "allowedDetentsCompat", "Array", "isArray", "Platform", "OS", "__DEV__", "console", "warn", "slice", "resolveSheetLargestUndimmedDetent", "lud", "lastDetentIndex", "isIndexInClosedRange", "resolveSheetInitialDetentIndex", "index", "lowerBound", "upperBound", "Number", "isInteger", "React", "forwardRef", "props", "ref", "innerRef", "useRef", "useImperativeHandle", "current", "prevActivityState", "usePrevious", "activityState", "setRef", "onComponentRef", "closing", "Value", "progress", "goingForward", "enabled", "screensEnabled", "freezeOnBlur", "freezeEnabled", "shouldFreeze", "rest", "sheetAllowedDetents", "sheetLargestUndimmedDetentIndex", "sheetGrabberVisible", "sheetCornerRadius", "sheetExpandsWhenScrolledToEdge", "sheetElevation", "sheetInitialDetentIndex", "stackPresentation", "onAppear", "onDisappear", "onWillAppear", "onWillDisappear", "isNativePlatformSupported", "resolvedSheetAllowedDetents", "resolvedSheetLargestUndimmedDetent", "resolvedSheetInitialDetentIndex", "shouldUseModalScreenComponent", "select", "ios", "undefined", "android", "AnimatedScreen", "active", "children", "isNativeStack", "gestureResponseDistance", "onGestureCancel", "style", "handleRef", "viewConfig", "validAttributes", "display", "_viewConfig", "freeze", "createElement", "zIndex", "sheetLargestUndimmedDetent", "sheetInitialDetent", "start", "end", "top", "bottom", "onTransitionProgress", "event", "nativeEvent", "useNativeDriver", "Provider", "View", "createContext", "Screen", "ScreenWrapper", "useContext", "displayName", "_default"], "sourceRoot": "../../../src", "sources": ["components/Screen.tsx"], "mappings": ";AAAA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,OAAA,GAAAF,OAAA,CAAAG,aAAA,GAAAH,OAAA,CAAAI,WAAA;AAEb,IAAAC,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,0BAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,cAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAGA,IAAAI,KAAA,GAAAJ,OAAA;AAOA,IAAAK,sBAAA,GAAAN,sBAAA,CAAAC,OAAA;AAGA,IAAAM,2BAAA,GAAAP,sBAAA,CAAAC,OAAA;AAGA,IAAAO,YAAA,GAAAP,OAAA;AAAoD,SAAAD,uBAAAS,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAb,OAAA,EAAAa,CAAA;AAAA,SAAAE,SAAA,WAAAA,QAAA,GAAAnB,MAAA,CAAAoB,MAAA,GAAApB,MAAA,CAAAoB,MAAA,CAAAC,IAAA,eAAAC,CAAA,aAAAL,CAAA,MAAAA,CAAA,GAAAM,SAAA,CAAAC,MAAA,EAAAP,CAAA,UAAAQ,CAAA,GAAAF,SAAA,CAAAN,CAAA,YAAAS,CAAA,IAAAD,CAAA,OAAAE,cAAA,CAAAC,IAAA,CAAAH,CAAA,EAAAC,CAAA,MAAAJ,CAAA,CAAAI,CAAA,IAAAD,CAAA,CAAAC,CAAA,aAAAJ,CAAA,KAAAH,QAAA,CAAAU,KAAA,OAAAN,SAAA,KAPpD;AAUA,MAAMO,oBAAoB,GAAGC,qBAAQ,CAACC,uBAAuB,CAC3DC,8BACF,CAAC;AACD,MAAMC,yBAAyB,GAAGH,qBAAQ,CAACC,uBAAuB,CAChEG,mCACF,CAAC;;AAED;AACA;;AAkBA;AACA,MAAMC,qBAAqB,GAAG,CAAC,CAAC,CAAC,CAAC;AAClC,MAAMC,kBAAkB,GAAG,CAAC,GAAG,CAAC;AAChC,MAAMC,mBAAmB,GAAG,CAAC,GAAG,CAAC;AACjC,MAAMC,gBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;AAEnC,MAAMC,mBAAmB,GAAG,CAAC,CAAC;AAC9B;;AAEA,SAASC,0BAA0BA,CAACC,KAAe,EAAE;EACnD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAAClB,MAAM,EAAEmB,CAAC,EAAE,EAAE;IACrC,IAAID,KAAK,CAACC,CAAC,GAAG,CAAC,CAAC,GAAGD,KAAK,CAACC,CAAC,CAAC,EAAE;MAC3B,MAAM,IAAIC,KAAK,CACb,gEACF,CAAC;IACH;EACF;AACF;;AAEA;AACA;AACA,SAASC,0BAA0BA,CACjCC,oBAAwD,EAC9C;EACV,IAAIC,KAAK,CAACC,OAAO,CAACF,oBAAoB,CAAC,EAAE;IACvC,IAAIG,qBAAQ,CAACC,EAAE,KAAK,SAAS,IAAIJ,oBAAoB,CAACtB,MAAM,GAAG,CAAC,EAAE;MAChE,IAAI2B,OAAO,EAAE;QACXC,OAAO,CAACC,IAAI,CACV,iGACF,CAAC;MACH;MACAP,oBAAoB,GAAGA,oBAAoB,CAACQ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACzD;IACA,IAAIH,OAAO,EAAE;MACXV,0BAA0B,CAACK,oBAAoB,CAAC;IAClD;IACA,OAAOA,oBAAoB;EAC7B,CAAC,MAAM,IAAIA,oBAAoB,KAAK,eAAe,EAAE;IACnD,OAAOV,qBAAqB;EAC9B,CAAC,MAAM,IAAIU,oBAAoB,KAAK,OAAO,EAAE;IAC3C,OAAOT,kBAAkB;EAC3B,CAAC,MAAM,IAAIS,oBAAoB,KAAK,QAAQ,EAAE;IAC5C,OAAOR,mBAAmB;EAC5B,CAAC,MAAM,IAAIQ,oBAAoB,KAAK,KAAK,EAAE;IACzC,OAAOP,gBAAgB;EACzB,CAAC,MAAM;IACL;IACA,OAAOF,kBAAkB;EAC3B;AACF;AAEA,SAASkB,iCAAiCA,CACxCC,GAAmD,EACnDC,eAAuB,EACf;EACR,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;IAC3B,IAAI,CAACE,oBAAoB,CAACF,GAAG,EAAEhB,mBAAmB,EAAEiB,eAAe,CAAC,EAAE;MACpE,IAAIN,OAAO,EAAE;QACX,MAAM,IAAIP,KAAK,CACb,uHACF,CAAC;MACH;MACA;MACA,OAAOJ,mBAAmB;IAC5B;IACA,OAAOgB,GAAG;EACZ,CAAC,MAAM,IAAIA,GAAG,KAAK,MAAM,EAAE;IACzB,OAAOC,eAAe;EACxB,CAAC,MAAM,IAAID,GAAG,KAAK,MAAM,IAAIA,GAAG,KAAK,KAAK,EAAE;IAC1C,OAAOhB,mBAAmB;EAC5B,CAAC,MAAM,IAAIgB,GAAG,KAAK,OAAO,EAAE;IAC1B,OAAO,CAAC;EACV,CAAC,MAAM,IAAIA,GAAG,KAAK,QAAQ,EAAE;IAC3B,OAAO,CAAC;EACV,CAAC,MAAM;IACL;IACA,OAAOhB,mBAAmB;EAC5B;AACF;AAEA,SAASmB,8BAA8BA,CACrCC,KAA6C,EAC7CH,eAAuB,EACf;EACR,IAAIG,KAAK,KAAK,MAAM,EAAE;IACpBA,KAAK,GAAGH,eAAe;EACzB,CAAC,MAAM,IAAIG,KAAK,IAAI,IAAI,EAAE;IACxB;IACAA,KAAK,GAAG,CAAC;EACX;EACA,IAAI,CAACF,oBAAoB,CAACE,KAAK,EAAE,CAAC,EAAEH,eAAe,CAAC,EAAE;IACpD,IAAIN,OAAO,EAAE;MACX,MAAM,IAAIP,KAAK,CACb,+GACF,CAAC;IACH;IACA;IACA,OAAO,CAAC;EACV;EACA,OAAOgB,KAAK;AACd;AAEA,SAASF,oBAAoBA,CAC3BvD,KAAa,EACb0D,UAAkB,EAClBC,UAAkB,EACT;EACT,OAAOC,MAAM,CAACC,SAAS,CAAC7D,KAAK,CAAC,IAAIA,KAAK,IAAI0D,UAAU,IAAI1D,KAAK,IAAI2D,UAAU;AAC9E;AAEO,MAAMxD,WAAW,GAAAJ,OAAA,CAAAI,WAAA,gBAAG2D,cAAK,CAACC,UAAU,CACzC,SAAS5D,WAAWA,CAAC6D,KAAK,EAAEC,GAAG,EAAE;EAC/B,MAAMC,QAAQ,GAAGJ,cAAK,CAACK,MAAM,CAAoB,IAAI,CAAC;EACtDL,cAAK,CAACM,mBAAmB,CAACH,GAAG,EAAE,MAAMC,QAAQ,CAACG,OAAQ,EAAE,EAAE,CAAC;EAC3D,MAAMC,iBAAiB,GAAG,IAAAC,wBAAW,EAACP,KAAK,CAACQ,aAAa,CAAC;EAE1D,MAAMC,MAAM,GAAIR,GAAe,IAAK;IAClCC,QAAQ,CAACG,OAAO,GAAGJ,GAAG;IACtBD,KAAK,CAACU,cAAc,GAAGT,GAAG,CAAC;EAC7B,CAAC;EAED,MAAMU,OAAO,GAAGb,cAAK,CAACK,MAAM,CAAC,IAAIvC,qBAAQ,CAACgD,KAAK,CAAC,CAAC,CAAC,CAAC,CAACP,OAAO;EAC3D,MAAMQ,QAAQ,GAAGf,cAAK,CAACK,MAAM,CAAC,IAAIvC,qBAAQ,CAACgD,KAAK,CAAC,CAAC,CAAC,CAAC,CAACP,OAAO;EAC5D,MAAMS,YAAY,GAAGhB,cAAK,CAACK,MAAM,CAAC,IAAIvC,qBAAQ,CAACgD,KAAK,CAAC,CAAC,CAAC,CAAC,CAACP,OAAO;EAEhE,MAAM;IACJU,OAAO,GAAG,IAAAC,oBAAc,EAAC,CAAC;IAC1BC,YAAY,GAAG,IAAAC,mBAAa,EAAC,CAAC;IAC9BC,YAAY;IACZ,GAAGC;EACL,CAAC,GAAGpB,KAAK;;EAET;EACA;EACA,MAAM;IACJ;IACAqB,mBAAmB,GAAG,CAAC,GAAG,CAAC;IAC3BC,+BAA+B,GAAGjD,mBAAmB;IACrDkD,mBAAmB,GAAG,KAAK;IAC3BC,iBAAiB,GAAG,CAAC,GAAG;IACxBC,8BAA8B,GAAG,IAAI;IACrCC,cAAc,GAAG,EAAE;IACnBC,uBAAuB,GAAG,CAAC;IAC3B;IACAC,iBAAiB;IACjB;IACAC,QAAQ;IACRC,WAAW;IACXC,YAAY;IACZC;EACF,CAAC,GAAGZ,IAAI;EAER,IAAIL,OAAO,IAAIkB,+BAAyB,EAAE;IACxC,MAAMC,2BAA2B,GAC/BxD,0BAA0B,CAAC2C,mBAAmB,CAAC;IACjD,MAAMc,kCAAkC,GACtC/C,iCAAiC,CAC/BkC,+BAA+B,EAC/BY,2BAA2B,CAAC7E,MAAM,GAAG,CACvC,CAAC;IACH,MAAM+E,+BAA+B,GAAG5C,8BAA8B,CACpEmC,uBAAuB,EACvBO,2BAA2B,CAAC7E,MAAM,GAAG,CACvC,CAAC;;IAED;IACA;IACA,MAAMgF,6BAA6B,GAAGvD,qBAAQ,CAACwD,MAAM,CAAC;MACpDC,GAAG,EAAE,EACHX,iBAAiB,KAAKY,SAAS,IAC/BZ,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,gBAAgB,IACtCA,iBAAiB,KAAK,2BAA2B,CAClD;MACDa,OAAO,EAAE,KAAK;MACdxG,OAAO,EAAE;IACX,CAAC,CAAC;IAEF,MAAMyG,cAAc,GAAGL,6BAA6B,GAChDtE,yBAAyB,GACzBJ,oBAAoB;IAExB,IAAI;MACF;MACA;MACA;MACAgF,MAAM;MACNnC,aAAa;MACboC,QAAQ;MACRC,aAAa;MACbC,uBAAuB;MACvBC,eAAe;MACfC,KAAK;MACL,GAAGhD;IACL,CAAC,GAAGoB,IAAI;IAER,IAAIuB,MAAM,KAAKH,SAAS,IAAIhC,aAAa,KAAKgC,SAAS,EAAE;MACvDvD,OAAO,CAACC,IAAI,CACV,+QACF,CAAC;MACDsB,aAAa,GAAGmC,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACxC;IAEA,IACEE,aAAa,IACbvC,iBAAiB,KAAKkC,SAAS,IAC/BhC,aAAa,KAAKgC,SAAS,EAC3B;MACA,IAAIlC,iBAAiB,GAAGE,aAAa,EAAE;QACrC,MAAM,IAAI/B,KAAK,CACb,8DACF,CAAC;MACH;IACF;IAEA,MAAMwE,SAAS,GAAIhD,GAAe,IAAK;MACrC;MACA;MACA,IAAIA,GAAG,EAAEiD,UAAU,EAAEC,eAAe,EAAEH,KAAK,EAAE;QAC3C/C,GAAG,CAACiD,UAAU,CAACC,eAAe,CAACH,KAAK,GAAG;UACrC,GAAG/C,GAAG,CAACiD,UAAU,CAACC,eAAe,CAACH,KAAK;UACvCI,OAAO,EAAE;QACX,CAAC;QACD3C,MAAM,CAACR,GAAG,CAAC;MACb,CAAC,MAAM,IAAIA,GAAG,EAAEoD,WAAW,EAAEF,eAAe,EAAEH,KAAK,EAAE;QACnD/C,GAAG,CAACoD,WAAW,CAACF,eAAe,CAACH,KAAK,GAAG;UACtC,GAAG/C,GAAG,CAACoD,WAAW,CAACF,eAAe,CAACH,KAAK;UACxCI,OAAO,EAAE;QACX,CAAC;QACD3C,MAAM,CAACR,GAAG,CAAC;MACb;IACF,CAAC;IAED,MAAMqD,MAAM,GACVrC,YAAY,KACXE,YAAY,KAAKqB,SAAS,GAAGrB,YAAY,GAAGX,aAAa,KAAK,CAAC,CAAC;IAEnE,oBACEpE,MAAA,CAAAH,OAAA,CAAAsH,aAAA,CAAC9G,cAAA,CAAAR,OAAa;MAACqH,MAAM,EAAEA;IAAO,gBAC5BlH,MAAA,CAAAH,OAAA,CAAAsH,aAAA,CAACb,cAAc,EAAA1F,QAAA,KACTgD,KAAK;MACT;AACZ;AACA;AACA;AACA;MACY6B,QAAQ,EAAEA,QAAoC;MAC9CC,WAAW,EAAEA,WAA0C;MACvDC,YAAY,EAAEA,YAA4C;MAC1DC,eAAe,EAAEA,eAAkD;MACnEe,eAAe,EACZA,eAAe,KACf,MAAM;QACL;MAAA,CACD;MAEH;MACA;MACA;MACA;MACA;MAAA;MACAC,KAAK,EAAE,CAACA,KAAK,EAAE;QAAEQ,MAAM,EAAEhB;MAAU,CAAC,CAAE;MACtChC,aAAa,EAAEA,aAAc;MAC7Ba,mBAAmB,EAAEa,2BAA4B;MACjDuB,0BAA0B,EAAEtB,kCAAmC;MAC/DT,cAAc,EAAEA,cAAe;MAC/BH,mBAAmB,EAAEA,mBAAoB;MACzCC,iBAAiB,EAAEA,iBAAkB;MACrCC,8BAA8B,EAAEA,8BAA+B;MAC/DiC,kBAAkB,EAAEtB,+BAAgC;MACpDU,uBAAuB,EAAE;QACvBa,KAAK,EAAEb,uBAAuB,EAAEa,KAAK,IAAI,CAAC,CAAC;QAC3CC,GAAG,EAAEd,uBAAuB,EAAEc,GAAG,IAAI,CAAC,CAAC;QACvCC,GAAG,EAAEf,uBAAuB,EAAEe,GAAG,IAAI,CAAC,CAAC;QACvCC,MAAM,EAAEhB,uBAAuB,EAAEgB,MAAM,IAAI,CAAC;MAC9C;MACA;MACA;MAAA;MACA7D,GAAG,EAAEgD,SAAU;MACfc,oBAAoB,EAClB,CAAClB,aAAa,GACVL,SAAS,GACT5E,qBAAQ,CAACoG,KAAK,CACZ,CACE;QACEC,WAAW,EAAE;UACXpD,QAAQ;UACRF,OAAO;UACPG;QACF;MACF,CAAC,CACF,EACD;QAAEoD,eAAe,EAAE;MAAK,CAC1B;IACL,IACA,CAACrB,aAAa;IAAK;IAClBD,QAAQ,gBAERxG,MAAA,CAAAH,OAAA,CAAAsH,aAAA,CAAC/G,0BAAA,CAAAP,OAAyB,CAACkI,QAAQ;MACjCnI,KAAK,EAAE;QACL6E,QAAQ;QACRF,OAAO;QACPG;MACF;IAAE,GACD8B,QACiC,CAExB,CACH,CAAC;EAEpB,CAAC,MAAM;IACL;IACA,IAAI;MACFD,MAAM;MACNnC,aAAa;MACbwC,KAAK;MACL;MACAtC,cAAc;MACd,GAAGV;IACL,CAAC,GAAGoB,IAAI;IAER,IAAIuB,MAAM,KAAKH,SAAS,IAAIhC,aAAa,KAAKgC,SAAS,EAAE;MACvDhC,aAAa,GAAGmC,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;IACtC;IACA,oBACEvG,MAAA,CAAAH,OAAA,CAAAsH,aAAA,CAAChH,YAAA,CAAAqB,QAAQ,CAACwG,IAAI,EAAApH,QAAA;MACZgG,KAAK,EAAE,CAACA,KAAK,EAAE;QAAEI,OAAO,EAAE5C,aAAa,KAAK,CAAC,GAAG,MAAM,GAAG;MAAO,CAAC,CAAE;MACnEP,GAAG,EAAEQ;IAAO,GACRT,KAAK,CACV,CAAC;EAEN;AACF,CACF,CAAC;;AAED;AACA;AACO,MAAM9D,aAAa,GAAAH,OAAA,CAAAG,aAAA,gBAAG4D,cAAK,CAACuE,aAAa,CAAClI,WAAW,CAAC;AAE7D,MAAMmI,MAAM,gBAAGxE,cAAK,CAACC,UAAU,CAAoB,CAACC,KAAK,EAAEC,GAAG,KAAK;EACjE,MAAMsE,aAAa,GAAGzE,cAAK,CAAC0E,UAAU,CAACtI,aAAa,CAAC,IAAIC,WAAW;EAEpE,oBAAOC,MAAA,CAAAH,OAAA,CAAAsH,aAAA,CAACgB,aAAa,EAAAvH,QAAA,KAAKgD,KAAK;IAAEC,GAAG,EAAEA;EAAI,EAAE,CAAC;AAC/C,CAAC,CAAC;AAEFqE,MAAM,CAACG,WAAW,GAAG,QAAQ;AAAC,IAAAC,QAAA,GAAA3I,OAAA,CAAAE,OAAA,GAEfqI,MAAM", "ignoreList": []}