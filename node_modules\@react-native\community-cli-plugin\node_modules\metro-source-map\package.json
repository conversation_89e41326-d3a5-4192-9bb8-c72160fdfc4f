{"name": "metro-source-map", "version": "0.82.4", "description": "🚇 Source map generator for Metro.", "main": "src/source-map.js", "exports": {".": "./src/source-map.js", "./package.json": "./package.json", "./private/*": "./src/*.js", "./src/*.js": "./src/*.js", "./src/*": "./src/*.js", "./src/Consumer": "./src/Consumer/index.js"}, "repository": {"type": "git", "url": "**************:facebook/metro.git"}, "scripts": {"prepare-release": "test -d build && rm -rf src.real && mv src src.real && mv build src", "cleanup-release": "test ! -e build && mv src build && mv src.real src"}, "dependencies": {"@babel/traverse": "^7.25.3", "@babel/traverse--for-generate-function-map": "npm:@babel/traverse@^7.25.3", "@babel/types": "^7.25.2", "flow-enums-runtime": "^0.0.6", "invariant": "^2.2.4", "metro-symbolicate": "0.82.4", "nullthrows": "^1.1.1", "ob1": "0.82.4", "source-map": "^0.5.6", "vlq": "^1.0.0"}, "license": "MIT", "devDependencies": {"@babel/core": "^7.25.2", "@babel/parser": "^7.25.3", "terser": "^5.15.0"}, "engines": {"node": ">=18.18"}}