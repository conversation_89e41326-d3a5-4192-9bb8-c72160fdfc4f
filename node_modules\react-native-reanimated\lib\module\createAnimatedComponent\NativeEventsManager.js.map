{"version": 3, "names": ["findNodeHandle", "WorkletEventHandler", "has", "NativeEventsManager", "managedComponent", "componentOptions", "eventViewTag", "constructor", "component", "options", "getEventViewTag", "attachEvents", "executeForEachEventHandler", "props", "key", "handler", "registerForEvents", "detachEvents", "_key", "unregisterFromEvents", "updateEvents", "prevProps", "computedEventTag", "prev<PERSON><PERSON><PERSON>", "newProp", "isWorkletEventHandler", "workletEventHandler", "componentUpdate", "componentAnimatedRef", "_componentRef", "getScrollableNode", "scrollableNode", "setNativeProps", "getComponentViewTag", "__nativeTag", "_nativeTag", "prop", "callback"], "sourceRoot": "../../../src", "sources": ["createAnimatedComponent/NativeEventsManager.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,cAAc,QAAQ,qCAAqC;AACpE,SAASC,mBAAmB,QAAQ,2BAAwB;AAQ5D,SAASC,GAAG,QAAQ,YAAS;AAE7B,OAAO,MAAMC,mBAAmB,CAAiC;EACtD,CAACC,gBAAgB;EACjB,CAACC,gBAAgB;EAC1B,CAACC,YAAY,GAAG,CAAC,CAAC;EAElBC,WAAWA,CAACC,SAAmC,EAAEC,OAA0B,EAAE;IAC3E,IAAI,CAAC,CAACL,gBAAgB,GAAGI,SAAS;IAClC,IAAI,CAAC,CAACH,gBAAgB,GAAGI,OAAO;IAChC,IAAI,CAAC,CAACH,YAAY,GAAG,IAAI,CAACI,eAAe,CAAC,CAAC;EAC7C;EAEOC,YAAYA,CAAA,EAAG;IACpBC,0BAA0B,CAAC,IAAI,CAAC,CAACR,gBAAgB,CAACS,KAAK,EAAE,CAACC,GAAG,EAAEC,OAAO,KAAK;MACzEA,OAAO,CAACC,iBAAiB,CAAC,IAAI,CAAC,CAACV,YAAY,EAAEQ,GAAG,CAAC;IACpD,CAAC,CAAC;EACJ;EAEOG,YAAYA,CAAA,EAAG;IACpBL,0BAA0B,CACxB,IAAI,CAAC,CAACR,gBAAgB,CAACS,KAAK,EAC5B,CAACK,IAAI,EAAEH,OAAO,KAAK;MACjBA,OAAO,CAACI,oBAAoB,CAAC,IAAI,CAAC,CAACb,YAAY,CAAC;IAClD,CACF,CAAC;EACH;EAEOc,YAAYA,CACjBC,SAAwD,EACxD;IACA,MAAMC,gBAAgB,GAAG,IAAI,CAACZ,eAAe,CAAC,IAAI,CAAC;IACnD;IACA,IAAI,IAAI,CAAC,CAACJ,YAAY,KAAKgB,gBAAgB,EAAE;MAC3C;MACAV,0BAA0B,CAACS,SAAS,EAAE,CAACH,IAAI,EAAEH,OAAO,KAAK;QACvDA,OAAO,CAACI,oBAAoB,CAAC,IAAI,CAAC,CAACb,YAAY,CAAC;MAClD,CAAC,CAAC;MACF;MACA;MACA,IAAI,CAAC,CAACA,YAAY,GAAGgB,gBAAgB;MACrC;MACA,IAAI,CAACX,YAAY,CAAC,CAAC;MACnB;IACF;IAEAC,0BAA0B,CAACS,SAAS,EAAE,CAACP,GAAG,EAAES,WAAW,KAAK;MAC1D,MAAMC,OAAO,GAAG,IAAI,CAAC,CAACpB,gBAAgB,CAACS,KAAK,CAACC,GAAG,CAAC;MACjD,IAAI,CAACU,OAAO,EAAE;QACZ;QACAD,WAAW,CAACJ,oBAAoB,CAAC,IAAI,CAAC,CAACb,YAAY,CAAC;MACtD,CAAC,MAAM,IACLmB,qBAAqB,CAACD,OAAO,CAAC,IAC9BA,OAAO,CAACE,mBAAmB,KAAKH,WAAW,EAC3C;QACA;QACAA,WAAW,CAACJ,oBAAoB,CAAC,IAAI,CAAC,CAACb,YAAY,CAAC;QACpDkB,OAAO,CAACE,mBAAmB,CAACV,iBAAiB,CAAC,IAAI,CAAC,CAACV,YAAY,CAAC;MACnE;IACF,CAAC,CAAC;IAEFM,0BAA0B,CAAC,IAAI,CAAC,CAACR,gBAAgB,CAACS,KAAK,EAAE,CAACC,GAAG,EAAEC,OAAO,KAAK;MACzE,IAAI,CAACM,SAAS,CAACP,GAAG,CAAC,EAAE;QACnB;QACAC,OAAO,CAACC,iBAAiB,CAAC,IAAI,CAAC,CAACV,YAAY,CAAC;MAC/C;IACF,CAAC,CAAC;EACJ;EAEQI,eAAeA,CAACiB,eAAwB,GAAG,KAAK,EAAE;IACxD;IACA,MAAMC,oBAAoB,GAAG,IAAI,CAAC,CAACxB,gBAAgB,CAChDyB,aAKF;IACD,IAAID,oBAAoB,CAACE,iBAAiB,EAAE;MAC1C;AACN;AACA;AACA;AACA;AACA;MACM,MAAMC,cAAc,GAAGH,oBAAoB,CAACE,iBAAiB,CAAC,CAAC;MAC/D,IAAI,OAAOC,cAAc,KAAK,QAAQ,EAAE;QACtC,OAAOA,cAAc;MACvB;MACA,OAAO/B,cAAc,CAAC+B,cAAc,CAAC,IAAI,CAAC,CAAC;IAC7C;IACA,IAAI,IAAI,CAAC,CAAC1B,gBAAgB,EAAE2B,cAAc,EAAE;MAC1C;MACA;MACA,OAAOhC,cAAc,CAAC,IAAI,CAAC,CAACI,gBAAgB,CAAC,IAAI,CAAC,CAAC;IACrD;IACA,IAAI,CAACuB,eAAe,EAAE;MACpB;MACA,OAAO,IAAI,CAAC,CAACvB,gBAAgB,CAAC6B,mBAAmB,CAAC,CAAC;IACrD;IACA,IAAIL,oBAAoB,CAACM,WAAW,IAAIN,oBAAoB,CAACO,UAAU,EAAE;MACvE;AACN;AACA;AACA;AACA;MACM,OACEP,oBAAoB,CAACM,WAAW,IAChCN,oBAAoB,CAACO,UAAU,IAC/B,CAAC,CAAC;IAEN;IACA;AACJ;AACA;AACA;IACI,OAAOnC,cAAc,CAAC4B,oBAAoB,CAAC,IAAI,CAAC,CAAC;EACnD;AACF;AAEA,SAASH,qBAAqBA,CAC5BW,IAAa,EACsB;EACnC,OACElC,GAAG,CAAC,qBAAqB,EAAEkC,IAAI,CAAC,IAChCA,IAAI,CAACV,mBAAmB,YAAYzB,mBAAmB;AAE3D;AAEA,SAASW,0BAA0BA,CACjCC,KAAoD,EACpDwB,QAGS,EACT;EACA,KAAK,MAAMvB,GAAG,IAAID,KAAK,EAAE;IACvB,MAAMuB,IAAI,GAAGvB,KAAK,CAACC,GAAG,CAAC;IACvB,IAAIW,qBAAqB,CAACW,IAAI,CAAC,EAAE;MAC/BC,QAAQ,CAACvB,GAAG,EAAEsB,IAAI,CAACV,mBAAmB,CAAC;IACzC;EACF;AACF", "ignoreList": []}