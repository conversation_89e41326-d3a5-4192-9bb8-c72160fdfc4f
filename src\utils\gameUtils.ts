import { Fish, FoodParticle, Position, FishOutlineColor } from '@/types';
import { FISH_SIZE_LEVELS, GAME_CONFIG, COLORS } from '@/utils/constants';
import { distance } from '@/utils/math';

/**
 * Get fish configuration for a given size level
 */
export function getFishConfig(sizeLevel: number) {
  const level = Math.max(1, Math.min(sizeLevel, FISH_SIZE_LEVELS.length));
  return FISH_SIZE_LEVELS[level - 1];
}

/**
 * Calculate score gained from eating a fish
 */
export function calculateFishScore(preyLevel: number): number {
  return preyLevel * 10 + 5; // Base score plus level bonus
}

/**
 * Calculate growth gained from eating a fish
 */
export function calculateFishGrowth(preyLevel: number): number {
  return Math.max(1, preyLevel * 0.5); // Minimum 1, scales with prey level
}

/**
 * Calculate growth gained from eating food
 */
export function calculateFoodGrowth(nutritionValue: number): number {
  return nutritionValue * 0.1;
}

/**
 * Determine outline color for a fish relative to the player
 */
export function getFishOutlineColor(playerLevel: number, fishLevel: number): FishOutlineColor {
  if (playerLevel > fishLevel) {
    return 'green'; // Can eat
  } else if (playerLevel < fishLevel) {
    return 'red'; // Dangerous
  } else {
    return 'yellow'; // Same size
  }
}

/**
 * Check if a fish is within vision range of the player
 */
export function isInVisionRange(
  playerPosition: Position,
  fishPosition: Position,
  visionRadius: number = GAME_CONFIG.vision.radius
): boolean {
  return distance(playerPosition, fishPosition) <= visionRadius;
}

/**
 * Get all fish within vision range
 */
export function getFishInVision(
  playerFish: Fish,
  allFish: Fish[],
  visionRadius: number = GAME_CONFIG.vision.radius
): Fish[] {
  return allFish.filter(fish => {
    if (fish.id === playerFish.id) return false;
    return isInVisionRange(playerFish.position, fish.position, visionRadius);
  });
}

/**
 * Get all food within vision range
 */
export function getFoodInVision(
  playerPosition: Position,
  allFood: FoodParticle[],
  visionRadius: number = GAME_CONFIG.vision.radius
): FoodParticle[] {
  return allFood.filter(food => {
    return isInVisionRange(playerPosition, food.position, visionRadius);
  });
}

/**
 * Calculate the speed for a fish based on its size level
 */
export function calculateFishSpeed(sizeLevel: number): number {
  const baseSpeed = GAME_CONFIG.player.baseSpeed;
  const speedDecay = GAME_CONFIG.player.speedDecayFactor;
  return baseSpeed * Math.pow(speedDecay, sizeLevel - 1);
}

/**
 * Format a score for display
 */
export function formatScore(score: number): string {
  if (score >= 1000000) {
    return `${(score / 1000000).toFixed(1)}M`;
  } else if (score >= 1000) {
    return `${(score / 1000).toFixed(1)}K`;
  }
  return score.toString();
}

/**
 * Format time duration for display
 */
export function formatTime(milliseconds: number): string {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  if (hours > 0) {
    return `${hours}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
  } else if (minutes > 0) {
    return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;
  } else {
    return `${seconds}s`;
  }
}

/**
 * Get a random fish name for AI fish
 */
export function getRandomFishName(): string {
  const names = [
    'Bubbles', 'Finn', 'Splash', 'Coral', 'Nemo', 'Dory', 'Gill', 'Bruce',
    'Marlin', 'Flounder', 'Sebastian', 'Ariel', 'Triton', 'Ursula', 'Scuttle',
    'Aqua', 'Marina', 'Ocean', 'Wave', 'Tide', 'Current', 'Deep', 'Blue',
    'Silver', 'Gold', 'Pearl', 'Shell', 'Reef', 'Kelp', 'Algae', 'Plankton'
  ];
  return names[Math.floor(Math.random() * names.length)];
}

/**
 * Calculate the optimal spawn distance from player
 */
export function getOptimalSpawnDistance(playerLevel: number): number {
  const baseDistance = 300;
  const levelMultiplier = 1.1;
  return baseDistance * Math.pow(levelMultiplier, playerLevel - 1);
}

/**
 * Check if a position is safe for spawning (not too close to large fish)
 */
export function isSafeSpawnPosition(
  position: Position,
  allFish: Fish[],
  minDistance: number = 100
): boolean {
  return !allFish.some(fish => {
    const dist = distance(position, fish.position);
    const safeDistance = fish.radius + minDistance;
    return dist < safeDistance;
  });
}

/**
 * Get the color for a fish based on its size level
 */
export function getFishColor(sizeLevel: number): string {
  const config = getFishConfig(sizeLevel);
  return config.color;
}

/**
 * Calculate the radius for a fish based on its size level
 */
export function getFishRadius(sizeLevel: number): number {
  const config = getFishConfig(sizeLevel);
  return config.radius;
}

/**
 * Get fish needed to grow for a given level
 */
export function getFishNeededToGrow(sizeLevel: number): number {
  const config = getFishConfig(sizeLevel);
  return config.fishNeeded;
}

/**
 * Calculate progress percentage for growth bar
 */
export function calculateGrowthPercentage(currentProgress: number, maxProgress: number): number {
  return Math.max(0, Math.min(100, (currentProgress / maxProgress) * 100));
}

/**
 * Get performance rating based on score
 */
export function getPerformanceRating(score: number): {
  title: string;
  color: string;
  description: string;
} {
  if (score >= 10000) {
    return {
      title: 'Ocean Predator',
      color: '#DC2626',
      description: 'Dominated the seas!',
    };
  } else if (score >= 5000) {
    return {
      title: 'Apex Fish',
      color: '#EA580C',
      description: 'Ruled the territory!',
    };
  } else if (score >= 2000) {
    return {
      title: 'Big Fish',
      color: '#7C3AED',
      description: 'Grew impressively large!',
    };
  } else if (score >= 500) {
    return {
      title: 'Growing Fish',
      color: '#2563EB',
      description: 'Made good progress!',
    };
  } else {
    return {
      title: 'Small Fry',
      color: '#10B981',
      description: 'Keep practicing!',
    };
  }
}

/**
 * Validate fish data integrity
 */
export function validateFish(fish: Fish): boolean {
  return !!(
    fish.id &&
    fish.position &&
    typeof fish.position.x === 'number' &&
    typeof fish.position.y === 'number' &&
    fish.sizeLevel >= 1 &&
    fish.sizeLevel <= FISH_SIZE_LEVELS.length &&
    fish.radius > 0 &&
    fish.score >= 0
  );
}

/**
 * Sanitize fish data for network transmission
 */
export function sanitizeFishData(fish: Fish): Partial<Fish> {
  return {
    id: fish.id,
    position: fish.position,
    velocity: fish.velocity,
    sizeLevel: fish.sizeLevel,
    radius: fish.radius,
    color: fish.color,
    isPlayer: fish.isPlayer,
    playerId: fish.playerId,
    playerName: fish.playerName,
    score: fish.score,
    isBoosting: fish.isBoosting,
    isAlive: fish.isAlive,
  };
}
