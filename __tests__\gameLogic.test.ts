import { 
  calculateFishScore, 
  calculateFishGrowth, 
  getFishOutlineColor,
  formatScore,
  validateFish,
  getFishConfig
} from '../src/utils/gameUtils';
import { Fish } from '../src/types';

describe('Game Logic Tests', () => {
  describe('Score Calculation', () => {
    test('should calculate correct score for eating fish', () => {
      expect(calculateFishScore(1)).toBe(15); // 1 * 10 + 5
      expect(calculateFishScore(5)).toBe(55); // 5 * 10 + 5
      expect(calculateFishScore(10)).toBe(105); // 10 * 10 + 5
    });

    test('should calculate correct growth for eating fish', () => {
      expect(calculateFishGrowth(1)).toBe(1); // Math.max(1, 1 * 0.5)
      expect(calculateFishGrowth(2)).toBe(1); // Math.max(1, 2 * 0.5)
      expect(calculateFishGrowth(4)).toBe(2); // Math.max(1, 4 * 0.5)
    });
  });

  describe('Fish Outline Colors', () => {
    test('should return green for smaller fish', () => {
      expect(getFishOutlineColor(5, 3)).toBe('green');
      expect(getFishOutlineColor(10, 1)).toBe('green');
    });

    test('should return red for larger fish', () => {
      expect(getFishOutlineColor(3, 5)).toBe('red');
      expect(getFishOutlineColor(1, 10)).toBe('red');
    });

    test('should return yellow for same size fish', () => {
      expect(getFishOutlineColor(5, 5)).toBe('yellow');
      expect(getFishOutlineColor(1, 1)).toBe('yellow');
    });
  });

  describe('Score Formatting', () => {
    test('should format small scores correctly', () => {
      expect(formatScore(0)).toBe('0');
      expect(formatScore(123)).toBe('123');
      expect(formatScore(999)).toBe('999');
    });

    test('should format thousands correctly', () => {
      expect(formatScore(1000)).toBe('1.0K');
      expect(formatScore(1500)).toBe('1.5K');
      expect(formatScore(999999)).toBe('1000.0K');
    });

    test('should format millions correctly', () => {
      expect(formatScore(1000000)).toBe('1.0M');
      expect(formatScore(2500000)).toBe('2.5M');
    });
  });

  describe('Fish Validation', () => {
    const validFish: Fish = {
      id: 'test-fish-1',
      position: { x: 100, y: 200 },
      velocity: { x: 0, y: 0 },
      sizeLevel: 5,
      radius: 50,
      color: '#3B82F6',
      isPlayer: true,
      playerId: 'player-1',
      playerName: 'Test Player',
      score: 1000,
      growthProgress: 50,
      fishNeededToGrow: 100,
      isBoosting: false,
      isAlive: true,
      lastUpdate: Date.now(),
    };

    test('should validate correct fish data', () => {
      expect(validateFish(validFish)).toBe(true);
    });

    test('should reject fish with invalid size level', () => {
      const invalidFish = { ...validFish, sizeLevel: 0 };
      expect(validateFish(invalidFish)).toBe(false);
    });

    test('should reject fish with invalid radius', () => {
      const invalidFish = { ...validFish, radius: -5 };
      expect(validateFish(invalidFish)).toBe(false);
    });

    test('should reject fish with invalid score', () => {
      const invalidFish = { ...validFish, score: -100 };
      expect(validateFish(invalidFish)).toBe(false);
    });
  });

  describe('Fish Configuration', () => {
    test('should return correct config for valid levels', () => {
      const level1 = getFishConfig(1);
      expect(level1.level).toBe(1);
      expect(level1.radius).toBe(20);

      const level5 = getFishConfig(5);
      expect(level5.level).toBe(5);
      expect(level5.radius).toBe(42);
    });

    test('should clamp invalid levels', () => {
      const tooLow = getFishConfig(0);
      expect(tooLow.level).toBe(1);

      const tooHigh = getFishConfig(999);
      expect(tooHigh.level).toBe(20);
    });
  });
});

// Mock test for React Native environment
jest.mock('react-native', () => ({
  Dimensions: {
    get: () => ({ width: 800, height: 600 }),
  },
}));
