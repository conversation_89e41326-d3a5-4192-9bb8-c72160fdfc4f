{"version": 3, "names": ["adaptViewConfig", "isSharedValue", "startMapper", "stopMapper", "updateProps", "makeViewDescriptorsSet", "flattenArray", "isInlineStyleTransform", "transform", "Array", "isArray", "some", "t", "hasInlineStyles", "inlinePropsHasChanged", "styles1", "styles2", "Object", "keys", "length", "key", "getInlinePropsUpdate", "inlineProps", "update", "styleValue", "entries", "value", "map", "item", "extractSharedValuesMapFromProps", "props", "styles", "style", "for<PERSON>ach", "styleKey", "getInlineStyle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "newStyle", "InlinePropManager", "_inlinePropsViewDescriptors", "_inlinePropsMapperId", "_inlineProps", "attachInlineProps", "animatedComponent", "viewInfo", "newInlineProps", "has<PERSON><PERSON>ed", "viewTag", "viewName", "shadowNodeWrapper", "viewConfig", "add", "tag", "name", "shareableViewDescriptors", "updaterFunction", "values", "detachInlineProps"], "sourceRoot": "../../../src", "sources": ["createAnimatedComponent/InlinePropManager.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,eAAe,QAAQ,oBAAiB;AACjD,SAASC,aAAa,QAAQ,qBAAkB;AAChD,SAASC,WAAW,EAAEC,UAAU,QAAQ,eAAY;AACpD,SAASC,WAAW,QAAQ,yBAAgB;AAE5C,SAASC,sBAAsB,QAAQ,0BAAuB;AAO9D,SAASC,YAAY,QAAQ,YAAS;AAEtC,SAASC,sBAAsBA,CAACC,SAAkB,EAAW;EAC3D,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,SAAS,CAAC,EAAE;IAC7B,OAAO,KAAK;EACd;EAEA,OAAOA,SAAS,CAACG,IAAI,CAAEC,CAA0B,IAAKC,eAAe,CAACD,CAAC,CAAC,CAAC;AAC3E;AAEA,SAASE,qBAAqBA,CAC5BC,OAAmB,EACnBC,OAAmB,EACV;EACT,IAAIC,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,CAACI,MAAM,KAAKF,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CAACG,MAAM,EAAE;IAC/D,OAAO,IAAI;EACb;EAEA,KAAK,MAAMC,GAAG,IAAIH,MAAM,CAACC,IAAI,CAACH,OAAO,CAAC,EAAE;IACtC,IAAIA,OAAO,CAACK,GAAG,CAAC,KAAKJ,OAAO,CAACI,GAAG,CAAC,EAAE;MACjC,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd;AAEA,SAASC,oBAAoBA,CAACC,WAAoC,EAAE;EAClE,SAAS;;EACT,MAAMC,MAA+B,GAAG,CAAC,CAAC;EAC1C,KAAK,MAAM,CAACH,GAAG,EAAEI,UAAU,CAAC,IAAIP,MAAM,CAACQ,OAAO,CAACH,WAAW,CAAC,EAAE;IAC3D,IAAIrB,aAAa,CAACuB,UAAU,CAAC,EAAE;MAC7BD,MAAM,CAACH,GAAG,CAAC,GAAGI,UAAU,CAACE,KAAK;IAChC,CAAC,MAAM,IAAIjB,KAAK,CAACC,OAAO,CAACc,UAAU,CAAC,EAAE;MACpCD,MAAM,CAACH,GAAG,CAAC,GAAGI,UAAU,CAACG,GAAG,CAAEC,IAAI,IAAK;QACrC,OAAOP,oBAAoB,CAACO,IAAI,CAAC;MACnC,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,OAAOJ,UAAU,KAAK,QAAQ,EAAE;MACzCD,MAAM,CAACH,GAAG,CAAC,GAAGC,oBAAoB,CAACG,UAAqC,CAAC;IAC3E,CAAC,MAAM;MACLD,MAAM,CAACH,GAAG,CAAC,GAAGI,UAAU;IAC1B;EACF;EACA,OAAOD,MAAM;AACf;AAEA,SAASM,+BAA+BA,CACtCC,KAEC,EACwB;EACzB,MAAMR,WAAoC,GAAG,CAAC,CAAC;EAE/C,KAAK,MAAMF,GAAG,IAAIU,KAAK,EAAE;IACvB,MAAMJ,KAAK,GAAGI,KAAK,CAACV,GAAG,CAAC;IACxB,IAAIA,GAAG,KAAK,OAAO,EAAE;MACnB,MAAMW,MAAM,GAAGzB,YAAY,CAAawB,KAAK,CAACE,KAAK,IAAI,EAAE,CAAC;MAC1DD,MAAM,CAACE,OAAO,CAAED,KAAK,IAAK;QACxB,IAAI,CAACA,KAAK,EAAE;UACV;QACF;QACA,KAAK,MAAM,CAACE,QAAQ,EAAEV,UAAU,CAAC,IAAIP,MAAM,CAACQ,OAAO,CAACO,KAAK,CAAC,EAAE;UAC1D,IAAI/B,aAAa,CAACuB,UAAU,CAAC,EAAE;YAC7BF,WAAW,CAACY,QAAQ,CAAC,GAAGV,UAAU;UACpC,CAAC,MAAM,IACLU,QAAQ,KAAK,WAAW,IACxB3B,sBAAsB,CAACiB,UAAU,CAAC,EAClC;YACAF,WAAW,CAACY,QAAQ,CAAC,GAAGV,UAAU;UACpC;QACF;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIvB,aAAa,CAACyB,KAAK,CAAC,EAAE;MAC/BJ,WAAW,CAACF,GAAG,CAAC,GAAGM,KAAK;IAC1B;EACF;EAEA,OAAOJ,WAAW;AACpB;AAEA,OAAO,SAAST,eAAeA,CAACmB,KAAiB,EAAW;EAC1D,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,KAAK;EACd;EACA,OAAOf,MAAM,CAACC,IAAI,CAACc,KAAK,CAAC,CAACrB,IAAI,CAAES,GAAG,IAAK;IACtC,MAAMI,UAAU,GAAGQ,KAAK,CAACZ,GAAG,CAAC;IAC7B,OACEnB,aAAa,CAACuB,UAAU,CAAC,IACxBJ,GAAG,KAAK,WAAW,IAAIb,sBAAsB,CAACiB,UAAU,CAAE;EAE/D,CAAC,CAAC;AACJ;AAEA,OAAO,SAASW,cAAcA,CAC5BH,KAA8B,EAC9BI,aAAsB,EACtB;EACA,IAAIA,aAAa,EAAE;IACjB,OAAOf,oBAAoB,CAACW,KAAK,CAAC;EACpC;EACA,MAAMK,QAAoB,GAAG,CAAC,CAAC;EAC/B,KAAK,MAAM,CAACjB,GAAG,EAAEI,UAAU,CAAC,IAAIP,MAAM,CAACQ,OAAO,CAACO,KAAK,CAAC,EAAE;IACrD,IACE,CAAC/B,aAAa,CAACuB,UAAU,CAAC,IAC1B,EAAEJ,GAAG,KAAK,WAAW,IAAIb,sBAAsB,CAACiB,UAAU,CAAC,CAAC,EAC5D;MACAa,QAAQ,CAACjB,GAAG,CAAC,GAAGI,UAAU;IAC5B;EACF;EACA,OAAOa,QAAQ;AACjB;AAEA,OAAO,MAAMC,iBAAiB,CAA+B;EAC3DC,2BAA2B,GAA8B,IAAI;EAC7DC,oBAAoB,GAAkB,IAAI;EAC1CC,YAAY,GAAe,CAAC,CAAC;EAEtBC,iBAAiBA,CACtBC,iBAC4B,EAC5BC,QAAkB,EAClB;IACA,MAAMC,cAAuC,GAC3ChB,+BAA+B,CAACc,iBAAiB,CAACb,KAAK,CAAC;IAC1D,MAAMgB,UAAU,GAAGhC,qBAAqB,CAAC+B,cAAc,EAAE,IAAI,CAACJ,YAAY,CAAC;IAE3E,IAAIK,UAAU,EAAE;MACd,IAAI,CAAC,IAAI,CAACP,2BAA2B,EAAE;QACrC,IAAI,CAACA,2BAA2B,GAAGlC,sBAAsB,CAAC,CAAC;QAE3D,MAAM;UAAE0C,OAAO;UAAEC,QAAQ;UAAEC,iBAAiB;UAAEC;QAAW,CAAC,GAAGN,QAAQ;QAErE,IAAI3B,MAAM,CAACC,IAAI,CAAC2B,cAAc,CAAC,CAAC1B,MAAM,IAAI+B,UAAU,EAAE;UACpDlD,eAAe,CAACkD,UAAU,CAAC;QAC7B;QAEA,IAAI,CAACX,2BAA2B,CAACY,GAAG,CAAC;UACnCC,GAAG,EAAEL,OAAiB;UACtBM,IAAI,EAAEL,QAAS;UACfC,iBAAiB,EAAEA;QACrB,CAAC,CAAC;MACJ;MACA,MAAMK,wBAAwB,GAC5B,IAAI,CAACf,2BAA2B,CAACe,wBAAwB;MAE3D,MAAMC,eAAe,GAAGA,CAAA,KAAM;QAC5B,SAAS;;QACT,MAAMhC,MAAM,GAAGF,oBAAoB,CAACwB,cAAc,CAAC;QACnDzC,WAAW,CAACkD,wBAAwB,EAAE/B,MAAM,CAAC;MAC/C,CAAC;MACD,IAAI,CAACkB,YAAY,GAAGI,cAAc;MAClC,IAAI,IAAI,CAACL,oBAAoB,EAAE;QAC7BrC,UAAU,CAAC,IAAI,CAACqC,oBAAoB,CAAC;MACvC;MACA,IAAI,CAACA,oBAAoB,GAAG,IAAI;MAChC,IAAIvB,MAAM,CAACC,IAAI,CAAC2B,cAAc,CAAC,CAAC1B,MAAM,EAAE;QACtC,IAAI,CAACqB,oBAAoB,GAAGtC,WAAW,CACrCqD,eAAe,EACftC,MAAM,CAACuC,MAAM,CAACX,cAAc,CAC9B,CAAC;MACH;IACF;EACF;EAEOY,iBAAiBA,CAAA,EAAG;IACzB,IAAI,IAAI,CAACjB,oBAAoB,EAAE;MAC7BrC,UAAU,CAAC,IAAI,CAACqC,oBAAoB,CAAC;IACvC;EACF;AACF", "ignoreList": []}