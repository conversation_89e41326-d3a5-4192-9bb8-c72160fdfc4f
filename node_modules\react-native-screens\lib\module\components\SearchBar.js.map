{"version": 3, "names": ["_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "React", "isSearchBarAvailableForCurrentPlatform", "View", "SearchBarNativeComponent", "Commands", "SearchBarNativeCommands", "NativeSearchBar", "NativeSearchBarCommands", "SearchBar", "props", "ref", "searchBarRef", "useRef", "useImperativeHandle", "blur", "_callMethodWithRef", "focus", "toggleCancelButton", "flag", "clearText", "setText", "text", "cancelSearch", "useCallback", "method", "current", "console", "warn", "createElement", "onSearchFocus", "onFocus", "onSearchBlur", "onBlur", "onSearchButtonPress", "onCancelButtonPress", "onChangeText", "forwardRef"], "sourceRoot": "../../../src", "sources": ["components/SearchBar.tsx"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,SAAA,WAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,CAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,CAAA,IAAAD,CAAA,OAAAE,cAAA,CAAAC,IAAA,CAAAH,CAAA,EAAAC,CAAA,MAAAL,CAAA,CAAAK,CAAA,IAAAD,CAAA,CAAAC,CAAA,aAAAL,CAAA,KAAAJ,QAAA,CAAAY,KAAA,OAAAN,SAAA;AAEb,OAAOO,KAAK,MAAM,OAAO;AAEzB,SAASC,sCAAsC,QAAQ,UAAU;AACjE,SAASC,IAAI,QAAQ,cAAc;;AAEnC;AACA,OAAOC,wBAAwB,IAC7BC,QAAQ,IAAIC,uBAAuB,QAK9B,oCAAoC;AAG3C,MAAMC,eAG0B,GAC9BH,wBACuB;AACzB,MAAMI,uBAA8C,GAClDF,uBAAgD;AAalD,SAASG,SAASA,CAACC,KAAqB,EAAEC,GAAiC,EAAE;EAC3E,MAAMC,YAAY,GAAGX,KAAK,CAACY,MAAM,CAA2B,IAAI,CAAC;EAEjEZ,KAAK,CAACa,mBAAmB,CAACH,GAAG,EAAE,OAAO;IACpCI,IAAI,EAAEA,CAAA,KAAM;MACVC,kBAAkB,CAACL,GAAG,IAAIH,uBAAuB,CAACO,IAAI,CAACJ,GAAG,CAAC,CAAC;IAC9D,CAAC;IACDM,KAAK,EAAEA,CAAA,KAAM;MACXD,kBAAkB,CAACL,GAAG,IAAIH,uBAAuB,CAACS,KAAK,CAACN,GAAG,CAAC,CAAC;IAC/D,CAAC;IACDO,kBAAkB,EAAGC,IAAa,IAAK;MACrCH,kBAAkB,CAACL,GAAG,IACpBH,uBAAuB,CAACU,kBAAkB,CAACP,GAAG,EAAEQ,IAAI,CACtD,CAAC;IACH,CAAC;IACDC,SAAS,EAAEA,CAAA,KAAM;MACfJ,kBAAkB,CAACL,GAAG,IAAIH,uBAAuB,CAACY,SAAS,CAACT,GAAG,CAAC,CAAC;IACnE,CAAC;IACDU,OAAO,EAAGC,IAAY,IAAK;MACzBN,kBAAkB,CAACL,GAAG,IAAIH,uBAAuB,CAACa,OAAO,CAACV,GAAG,EAAEW,IAAI,CAAC,CAAC;IACvE,CAAC;IACDC,YAAY,EAAEA,CAAA,KAAM;MAClBP,kBAAkB,CAACL,GAAG,IAAIH,uBAAuB,CAACe,YAAY,CAACZ,GAAG,CAAC,CAAC;IACtE;EACF,CAAC,CAAC,CAAC;EAEH,MAAMK,kBAAkB,GAAGf,KAAK,CAACuB,WAAW,CACzCC,MAAwC,IAAK;IAC5C,MAAMd,GAAG,GAAGC,YAAY,CAACc,OAAO;IAChC,IAAIf,GAAG,EAAE;MACPc,MAAM,CAACd,GAAG,CAAC;IACb,CAAC,MAAM;MACLgB,OAAO,CAACC,IAAI,CACV,mEACF,CAAC;IACH;EACF,CAAC,EACD,CAAChB,YAAY,CACf,CAAC;EAED,IAAI,CAACV,sCAAsC,EAAE;IAC3CyB,OAAO,CAACC,IAAI,CACV,+DACF,CAAC;IACD,OAAOzB,IAAI;EACb;EAEA,oBACEF,KAAA,CAAA4B,aAAA,CAACtB,eAAe,EAAAnB,QAAA;IACduB,GAAG,EAAEC;EAAa,GACdF,KAAK;IACToB,aAAa,EAAEpB,KAAK,CAACqB,OAA8C;IACnEC,YAAY,EAAEtB,KAAK,CAACuB,MAA6C;IACjEC,mBAAmB,EACjBxB,KAAK,CAACwB,mBACP;IACDC,mBAAmB,EACjBzB,KAAK,CAACyB,mBACP;IACDC,YAAY,EAAE1B,KAAK,CAAC0B;EAAoD,EACzE,CAAC;AAEN;AAEA,4BAAenC,KAAK,CAACoC,UAAU,CAAoC5B,SAAS,CAAC", "ignoreList": []}