#!/usr/bin/env node

/**
 * Development setup script for Fish Eat Fish 2
 * This script helps set up the development environment
 */

const fs = require('fs');
const path = require('path');

console.log('🐟 Fish Eat Fish 2 - Development Setup');
console.log('=====================================\n');

// Check if we're in the right directory
const packageJsonPath = path.join(process.cwd(), 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ Error: package.json not found. Please run this script from the project root.');
  process.exit(1);
}

// Read package.json to verify project
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
if (packageJson.name !== 'fisheatfish2') {
  console.error('❌ Error: This doesn\'t appear to be the Fish Eat Fish 2 project.');
  process.exit(1);
}

console.log('✅ Project verified: Fish Eat Fish 2');

// Check Node.js version
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
if (majorVersion < 16) {
  console.warn('⚠️  Warning: Node.js 16+ is recommended. Current version:', nodeVersion);
} else {
  console.log('✅ Node.js version:', nodeVersion);
}

// Check if node_modules exists
const nodeModulesPath = path.join(process.cwd(), 'node_modules');
if (!fs.existsSync(nodeModulesPath)) {
  console.log('📦 Installing dependencies...');
  const { execSync } = require('child_process');
  try {
    execSync('npm install', { stdio: 'inherit' });
    console.log('✅ Dependencies installed');
  } catch (error) {
    console.error('❌ Error installing dependencies:', error.message);
    process.exit(1);
  }
} else {
  console.log('✅ Dependencies already installed');
}

// Create assets directory if it doesn't exist
const assetsDir = path.join(process.cwd(), 'src', 'assets');
if (!fs.existsSync(assetsDir)) {
  fs.mkdirSync(assetsDir, { recursive: true });
  console.log('✅ Created assets directory');
} else {
  console.log('✅ Assets directory exists');
}

// Check TypeScript compilation
console.log('🔍 Checking TypeScript compilation...');
try {
  const { execSync } = require('child_process');
  execSync('npx tsc --noEmit', { stdio: 'pipe' });
  console.log('✅ TypeScript compilation successful');
} catch (error) {
  console.error('❌ TypeScript compilation errors found');
  console.error(error.stdout?.toString() || error.message);
}

// Run tests
console.log('🧪 Running tests...');
try {
  const { execSync } = require('child_process');
  execSync('npm test -- --passWithNoTests', { stdio: 'pipe' });
  console.log('✅ All tests passing');
} catch (error) {
  console.error('❌ Some tests failed');
  console.error(error.stdout?.toString() || error.message);
}

// Display project structure
console.log('\n📁 Project Structure:');
console.log('├── src/');
console.log('│   ├── components/     # Reusable UI components');
console.log('│   ├── screens/        # Screen components');
console.log('│   ├── game/           # Game engine and logic');
console.log('│   │   ├── engine/     # Core game engine');
console.log('│   │   ├── entities/   # Game entities');
console.log('│   │   └── systems/    # Game systems');
console.log('│   ├── store/          # State management');
console.log('│   ├── services/       # External services');
console.log('│   ├── types/          # TypeScript definitions');
console.log('│   └── utils/          # Utility functions');
console.log('├── __tests__/          # Test files');
console.log('└── scripts/            # Development scripts');

// Display available commands
console.log('\n🚀 Available Commands:');
console.log('npm start              # Start Metro bundler');
console.log('npm run android        # Run on Android');
console.log('npm run ios            # Run on iOS');
console.log('npm test               # Run tests');
console.log('npm run lint           # Run ESLint');
console.log('npx tsc --noEmit       # Check TypeScript');

// Display next steps
console.log('\n📋 Next Steps:');
console.log('1. Start Metro bundler: npm start');
console.log('2. In another terminal, run: npm run android (or npm run ios)');
console.log('3. The game should launch on your device/emulator');

// Display game features
console.log('\n🎮 Game Features Implemented:');
console.log('✅ Main Menu with player name input');
console.log('✅ Tutorial screen with game instructions');
console.log('✅ Core game engine with physics');
console.log('✅ Virtual joystick controls');
console.log('✅ Fish entities with growth mechanics');
console.log('✅ Food particle system');
console.log('✅ Collision detection and eating');
console.log('✅ Boost system with progress consumption');
console.log('✅ Visual indicators (colored outlines)');
console.log('✅ Progress bar and level system');
console.log('✅ Leaderboard component');
console.log('✅ Minimap with vision area');
console.log('✅ Game over screen with statistics');
console.log('✅ State management with Zustand');
console.log('✅ TypeScript for type safety');
console.log('✅ Comprehensive test suite');

console.log('\n🔮 Future Enhancements:');
console.log('🔄 Multiplayer server implementation');
console.log('🔄 Real-time networking with WebSockets');
console.log('🔄 Sound effects and background music');
console.log('🔄 Particle effects and animations');
console.log('🔄 Achievement system');
console.log('🔄 Customizable fish skins');
console.log('🔄 Different game modes');

console.log('\n🎉 Setup complete! Happy coding! 🐟');
