{"version": 3, "names": ["_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "React", "Animated", "Platform", "TransitionProgressContext", "DelayedFreeze", "freezeEnabled", "isNativePlatformSupported", "screensEnabled", "ScreenNativeComponent", "ModalScreenNativeComponent", "usePrevious", "AnimatedNativeScreen", "createAnimatedComponent", "AnimatedNativeModalScreen", "SHEET_FIT_TO_CONTENTS", "SHEET_COMPAT_LARGE", "SHEET_COMPAT_MEDIUM", "SHEET_COMPAT_ALL", "SHEET_DIMMED_ALWAYS", "assertDetentsArrayIsSorted", "array", "i", "Error", "resolveSheetAllowedDetents", "allowedDetentsCompat", "Array", "isArray", "OS", "__DEV__", "console", "warn", "slice", "resolveSheetLargestUndimmedDetent", "lud", "lastDetentIndex", "isIndexInClosedRange", "resolveSheetInitialDetentIndex", "index", "value", "lowerBound", "upperBound", "Number", "isInteger", "InnerScreen", "forwardRef", "props", "ref", "innerRef", "useRef", "useImperativeHandle", "current", "prevActivityState", "activityState", "setRef", "onComponentRef", "closing", "Value", "progress", "goingForward", "enabled", "freezeOnBlur", "shouldFreeze", "rest", "sheetAllowedDetents", "sheetLargestUndimmedDetentIndex", "sheetGrabberVisible", "sheetCornerRadius", "sheetExpandsWhenScrolledToEdge", "sheetElevation", "sheetInitialDetentIndex", "stackPresentation", "onAppear", "onDisappear", "onWillAppear", "onWillDisappear", "resolvedSheetAllowedDetents", "resolvedSheetLargestUndimmedDetent", "resolvedSheetInitialDetentIndex", "shouldUseModalScreenComponent", "select", "ios", "undefined", "android", "default", "AnimatedScreen", "active", "children", "isNativeStack", "gestureResponseDistance", "onGestureCancel", "style", "handleRef", "viewConfig", "validAttributes", "display", "_viewConfig", "freeze", "createElement", "zIndex", "sheetLargestUndimmedDetent", "sheetInitialDetent", "start", "end", "top", "bottom", "onTransitionProgress", "event", "nativeEvent", "useNativeDriver", "Provider", "View", "ScreenContext", "createContext", "Screen", "ScreenWrapper", "useContext", "displayName"], "sourceRoot": "../../../src", "sources": ["components/Screen.tsx"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,SAAA,WAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,CAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,CAAA,IAAAD,CAAA,OAAAE,cAAA,CAAAC,IAAA,CAAAH,CAAA,EAAAC,CAAA,MAAAL,CAAA,CAAAK,CAAA,IAAAD,CAAA,CAAAC,CAAA,aAAAL,CAAA,KAAAJ,QAAA,CAAAY,KAAA,OAAAN,SAAA;AAEb,OAAOO,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAQC,QAAQ,QAAQ,cAAc;AAEvD,OAAOC,yBAAyB,MAAM,8BAA8B;AACpE,OAAOC,aAAa,MAAM,yBAAyB;AAGnD,SACEC,aAAa,EACbC,yBAAyB,EACzBC,cAAc,QACT,SAAS;;AAEhB;AACA,OAAOC,qBAAqB,MAErB,iCAAiC;AACxC,OAAOC,0BAA0B,MAE1B,sCAAsC;AAC7C,SAASC,WAAW,QAAQ,uBAAuB;AAGnD,MAAMC,oBAAoB,GAAGV,QAAQ,CAACW,uBAAuB,CAC3DJ,qBACF,CAAC;AACD,MAAMK,yBAAyB,GAAGZ,QAAQ,CAACW,uBAAuB,CAChEH,0BACF,CAAC;;AAED;AACA;;AAkBA;AACA,MAAMK,qBAAqB,GAAG,CAAC,CAAC,CAAC,CAAC;AAClC,MAAMC,kBAAkB,GAAG,CAAC,GAAG,CAAC;AAChC,MAAMC,mBAAmB,GAAG,CAAC,GAAG,CAAC;AACjC,MAAMC,gBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;AAEnC,MAAMC,mBAAmB,GAAG,CAAC,CAAC;AAC9B;;AAEA,SAASC,0BAA0BA,CAACC,KAAe,EAAE;EACnD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAAC1B,MAAM,EAAE2B,CAAC,EAAE,EAAE;IACrC,IAAID,KAAK,CAACC,CAAC,GAAG,CAAC,CAAC,GAAGD,KAAK,CAACC,CAAC,CAAC,EAAE;MAC3B,MAAM,IAAIC,KAAK,CACb,gEACF,CAAC;IACH;EACF;AACF;;AAEA;AACA;AACA,SAASC,0BAA0BA,CACjCC,oBAAwD,EAC9C;EACV,IAAIC,KAAK,CAACC,OAAO,CAACF,oBAAoB,CAAC,EAAE;IACvC,IAAItB,QAAQ,CAACyB,EAAE,KAAK,SAAS,IAAIH,oBAAoB,CAAC9B,MAAM,GAAG,CAAC,EAAE;MAChE,IAAIkC,OAAO,EAAE;QACXC,OAAO,CAACC,IAAI,CACV,iGACF,CAAC;MACH;MACAN,oBAAoB,GAAGA,oBAAoB,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACzD;IACA,IAAIH,OAAO,EAAE;MACXT,0BAA0B,CAACK,oBAAoB,CAAC;IAClD;IACA,OAAOA,oBAAoB;EAC7B,CAAC,MAAM,IAAIA,oBAAoB,KAAK,eAAe,EAAE;IACnD,OAAOV,qBAAqB;EAC9B,CAAC,MAAM,IAAIU,oBAAoB,KAAK,OAAO,EAAE;IAC3C,OAAOT,kBAAkB;EAC3B,CAAC,MAAM,IAAIS,oBAAoB,KAAK,QAAQ,EAAE;IAC5C,OAAOR,mBAAmB;EAC5B,CAAC,MAAM,IAAIQ,oBAAoB,KAAK,KAAK,EAAE;IACzC,OAAOP,gBAAgB;EACzB,CAAC,MAAM;IACL;IACA,OAAOF,kBAAkB;EAC3B;AACF;AAEA,SAASiB,iCAAiCA,CACxCC,GAAmD,EACnDC,eAAuB,EACf;EACR,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;IAC3B,IAAI,CAACE,oBAAoB,CAACF,GAAG,EAAEf,mBAAmB,EAAEgB,eAAe,CAAC,EAAE;MACpE,IAAIN,OAAO,EAAE;QACX,MAAM,IAAIN,KAAK,CACb,uHACF,CAAC;MACH;MACA;MACA,OAAOJ,mBAAmB;IAC5B;IACA,OAAOe,GAAG;EACZ,CAAC,MAAM,IAAIA,GAAG,KAAK,MAAM,EAAE;IACzB,OAAOC,eAAe;EACxB,CAAC,MAAM,IAAID,GAAG,KAAK,MAAM,IAAIA,GAAG,KAAK,KAAK,EAAE;IAC1C,OAAOf,mBAAmB;EAC5B,CAAC,MAAM,IAAIe,GAAG,KAAK,OAAO,EAAE;IAC1B,OAAO,CAAC;EACV,CAAC,MAAM,IAAIA,GAAG,KAAK,QAAQ,EAAE;IAC3B,OAAO,CAAC;EACV,CAAC,MAAM;IACL;IACA,OAAOf,mBAAmB;EAC5B;AACF;AAEA,SAASkB,8BAA8BA,CACrCC,KAA6C,EAC7CH,eAAuB,EACf;EACR,IAAIG,KAAK,KAAK,MAAM,EAAE;IACpBA,KAAK,GAAGH,eAAe;EACzB,CAAC,MAAM,IAAIG,KAAK,IAAI,IAAI,EAAE;IACxB;IACAA,KAAK,GAAG,CAAC;EACX;EACA,IAAI,CAACF,oBAAoB,CAACE,KAAK,EAAE,CAAC,EAAEH,eAAe,CAAC,EAAE;IACpD,IAAIN,OAAO,EAAE;MACX,MAAM,IAAIN,KAAK,CACb,+GACF,CAAC;IACH;IACA;IACA,OAAO,CAAC;EACV;EACA,OAAOe,KAAK;AACd;AAEA,SAASF,oBAAoBA,CAC3BG,KAAa,EACbC,UAAkB,EAClBC,UAAkB,EACT;EACT,OAAOC,MAAM,CAACC,SAAS,CAACJ,KAAK,CAAC,IAAIA,KAAK,IAAIC,UAAU,IAAID,KAAK,IAAIE,UAAU;AAC9E;AAEA,OAAO,MAAMG,WAAW,gBAAG3C,KAAK,CAAC4C,UAAU,CACzC,SAASD,WAAWA,CAACE,KAAK,EAAEC,GAAG,EAAE;EAC/B,MAAMC,QAAQ,GAAG/C,KAAK,CAACgD,MAAM,CAAoB,IAAI,CAAC;EACtDhD,KAAK,CAACiD,mBAAmB,CAACH,GAAG,EAAE,MAAMC,QAAQ,CAACG,OAAQ,EAAE,EAAE,CAAC;EAC3D,MAAMC,iBAAiB,GAAGzC,WAAW,CAACmC,KAAK,CAACO,aAAa,CAAC;EAE1D,MAAMC,MAAM,GAAIP,GAAe,IAAK;IAClCC,QAAQ,CAACG,OAAO,GAAGJ,GAAG;IACtBD,KAAK,CAACS,cAAc,GAAGR,GAAG,CAAC;EAC7B,CAAC;EAED,MAAMS,OAAO,GAAGvD,KAAK,CAACgD,MAAM,CAAC,IAAI/C,QAAQ,CAACuD,KAAK,CAAC,CAAC,CAAC,CAAC,CAACN,OAAO;EAC3D,MAAMO,QAAQ,GAAGzD,KAAK,CAACgD,MAAM,CAAC,IAAI/C,QAAQ,CAACuD,KAAK,CAAC,CAAC,CAAC,CAAC,CAACN,OAAO;EAC5D,MAAMQ,YAAY,GAAG1D,KAAK,CAACgD,MAAM,CAAC,IAAI/C,QAAQ,CAACuD,KAAK,CAAC,CAAC,CAAC,CAAC,CAACN,OAAO;EAEhE,MAAM;IACJS,OAAO,GAAGpD,cAAc,CAAC,CAAC;IAC1BqD,YAAY,GAAGvD,aAAa,CAAC,CAAC;IAC9BwD,YAAY;IACZ,GAAGC;EACL,CAAC,GAAGjB,KAAK;;EAET;EACA;EACA,MAAM;IACJ;IACAkB,mBAAmB,GAAG,CAAC,GAAG,CAAC;IAC3BC,+BAA+B,GAAG9C,mBAAmB;IACrD+C,mBAAmB,GAAG,KAAK;IAC3BC,iBAAiB,GAAG,CAAC,GAAG;IACxBC,8BAA8B,GAAG,IAAI;IACrCC,cAAc,GAAG,EAAE;IACnBC,uBAAuB,GAAG,CAAC;IAC3B;IACAC,iBAAiB;IACjB;IACAC,QAAQ;IACRC,WAAW;IACXC,YAAY;IACZC;EACF,CAAC,GAAGZ,IAAI;EAER,IAAIH,OAAO,IAAIrD,yBAAyB,EAAE;IACxC,MAAMqE,2BAA2B,GAC/BpD,0BAA0B,CAACwC,mBAAmB,CAAC;IACjD,MAAMa,kCAAkC,GACtC5C,iCAAiC,CAC/BgC,+BAA+B,EAC/BW,2BAA2B,CAACjF,MAAM,GAAG,CACvC,CAAC;IACH,MAAMmF,+BAA+B,GAAGzC,8BAA8B,CACpEiC,uBAAuB,EACvBM,2BAA2B,CAACjF,MAAM,GAAG,CACvC,CAAC;;IAED;IACA;IACA,MAAMoF,6BAA6B,GAAG5E,QAAQ,CAAC6E,MAAM,CAAC;MACpDC,GAAG,EAAE,EACHV,iBAAiB,KAAKW,SAAS,IAC/BX,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,gBAAgB,IACtCA,iBAAiB,KAAK,2BAA2B,CAClD;MACDY,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACX,CAAC,CAAC;IAEF,MAAMC,cAAc,GAAGN,6BAA6B,GAChDjE,yBAAyB,GACzBF,oBAAoB;IAExB,IAAI;MACF;MACA;MACA;MACA0E,MAAM;MACNjC,aAAa;MACbkC,QAAQ;MACRC,aAAa;MACbC,uBAAuB;MACvBC,eAAe;MACfC,KAAK;MACL,GAAG7C;IACL,CAAC,GAAGiB,IAAI;IAER,IAAIuB,MAAM,KAAKJ,SAAS,IAAI7B,aAAa,KAAK6B,SAAS,EAAE;MACvDpD,OAAO,CAACC,IAAI,CACV,+QACF,CAAC;MACDsB,aAAa,GAAGiC,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACxC;IAEA,IACEE,aAAa,IACbpC,iBAAiB,KAAK8B,SAAS,IAC/B7B,aAAa,KAAK6B,SAAS,EAC3B;MACA,IAAI9B,iBAAiB,GAAGC,aAAa,EAAE;QACrC,MAAM,IAAI9B,KAAK,CACb,8DACF,CAAC;MACH;IACF;IAEA,MAAMqE,SAAS,GAAI7C,GAAe,IAAK;MACrC;MACA;MACA,IAAIA,GAAG,EAAE8C,UAAU,EAAEC,eAAe,EAAEH,KAAK,EAAE;QAC3C5C,GAAG,CAAC8C,UAAU,CAACC,eAAe,CAACH,KAAK,GAAG;UACrC,GAAG5C,GAAG,CAAC8C,UAAU,CAACC,eAAe,CAACH,KAAK;UACvCI,OAAO,EAAE;QACX,CAAC;QACDzC,MAAM,CAACP,GAAG,CAAC;MACb,CAAC,MAAM,IAAIA,GAAG,EAAEiD,WAAW,EAAEF,eAAe,EAAEH,KAAK,EAAE;QACnD5C,GAAG,CAACiD,WAAW,CAACF,eAAe,CAACH,KAAK,GAAG;UACtC,GAAG5C,GAAG,CAACiD,WAAW,CAACF,eAAe,CAACH,KAAK;UACxCI,OAAO,EAAE;QACX,CAAC;QACDzC,MAAM,CAACP,GAAG,CAAC;MACb;IACF,CAAC;IAED,MAAMkD,MAAM,GACVpC,YAAY,KACXC,YAAY,KAAKoB,SAAS,GAAGpB,YAAY,GAAGT,aAAa,KAAK,CAAC,CAAC;IAEnE,oBACEpD,KAAA,CAAAiG,aAAA,CAAC7F,aAAa;MAAC4F,MAAM,EAAEA;IAAO,gBAC5BhG,KAAA,CAAAiG,aAAA,CAACb,cAAc,EAAAjG,QAAA,KACT0D,KAAK;MACT;AACZ;AACA;AACA;AACA;MACY0B,QAAQ,EAAEA,QAAoC;MAC9CC,WAAW,EAAEA,WAA0C;MACvDC,YAAY,EAAEA,YAA4C;MAC1DC,eAAe,EAAEA,eAAkD;MACnEe,eAAe,EACZA,eAAe,KACf,MAAM;QACL;MAAA,CACD;MAEH;MACA;MACA;MACA;MACA;MAAA;MACAC,KAAK,EAAE,CAACA,KAAK,EAAE;QAAEQ,MAAM,EAAEjB;MAAU,CAAC,CAAE;MACtC7B,aAAa,EAAEA,aAAc;MAC7BW,mBAAmB,EAAEY,2BAA4B;MACjDwB,0BAA0B,EAAEvB,kCAAmC;MAC/DR,cAAc,EAAEA,cAAe;MAC/BH,mBAAmB,EAAEA,mBAAoB;MACzCC,iBAAiB,EAAEA,iBAAkB;MACrCC,8BAA8B,EAAEA,8BAA+B;MAC/DiC,kBAAkB,EAAEvB,+BAAgC;MACpDW,uBAAuB,EAAE;QACvBa,KAAK,EAAEb,uBAAuB,EAAEa,KAAK,IAAI,CAAC,CAAC;QAC3CC,GAAG,EAAEd,uBAAuB,EAAEc,GAAG,IAAI,CAAC,CAAC;QACvCC,GAAG,EAAEf,uBAAuB,EAAEe,GAAG,IAAI,CAAC,CAAC;QACvCC,MAAM,EAAEhB,uBAAuB,EAAEgB,MAAM,IAAI,CAAC;MAC9C;MACA;MACA;MAAA;MACA1D,GAAG,EAAE6C,SAAU;MACfc,oBAAoB,EAClB,CAAClB,aAAa,GACVN,SAAS,GACThF,QAAQ,CAACyG,KAAK,CACZ,CACE;QACEC,WAAW,EAAE;UACXlD,QAAQ;UACRF,OAAO;UACPG;QACF;MACF,CAAC,CACF,EACD;QAAEkD,eAAe,EAAE;MAAK,CAC1B;IACL,IACA,CAACrB,aAAa;IAAK;IAClBD,QAAQ,gBAERtF,KAAA,CAAAiG,aAAA,CAAC9F,yBAAyB,CAAC0G,QAAQ;MACjCvE,KAAK,EAAE;QACLmB,QAAQ;QACRF,OAAO;QACPG;MACF;IAAE,GACD4B,QACiC,CAExB,CACH,CAAC;EAEpB,CAAC,MAAM;IACL;IACA,IAAI;MACFD,MAAM;MACNjC,aAAa;MACbsC,KAAK;MACL;MACApC,cAAc;MACd,GAAGT;IACL,CAAC,GAAGiB,IAAI;IAER,IAAIuB,MAAM,KAAKJ,SAAS,IAAI7B,aAAa,KAAK6B,SAAS,EAAE;MACvD7B,aAAa,GAAGiC,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;IACtC;IACA,oBACErF,KAAA,CAAAiG,aAAA,CAAChG,QAAQ,CAAC6G,IAAI,EAAA3H,QAAA;MACZuG,KAAK,EAAE,CAACA,KAAK,EAAE;QAAEI,OAAO,EAAE1C,aAAa,KAAK,CAAC,GAAG,MAAM,GAAG;MAAO,CAAC,CAAE;MACnEN,GAAG,EAAEO;IAAO,GACRR,KAAK,CACV,CAAC;EAEN;AACF,CACF,CAAC;;AAED;AACA;AACA,OAAO,MAAMkE,aAAa,gBAAG/G,KAAK,CAACgH,aAAa,CAACrE,WAAW,CAAC;AAE7D,MAAMsE,MAAM,gBAAGjH,KAAK,CAAC4C,UAAU,CAAoB,CAACC,KAAK,EAAEC,GAAG,KAAK;EACjE,MAAMoE,aAAa,GAAGlH,KAAK,CAACmH,UAAU,CAACJ,aAAa,CAAC,IAAIpE,WAAW;EAEpE,oBAAO3C,KAAA,CAAAiG,aAAA,CAACiB,aAAa,EAAA/H,QAAA,KAAK0D,KAAK;IAAEC,GAAG,EAAEA;EAAI,EAAE,CAAC;AAC/C,CAAC,CAAC;AAEFmE,MAAM,CAACG,WAAW,GAAG,QAAQ;AAE7B,eAAeH,MAAM", "ignoreList": []}