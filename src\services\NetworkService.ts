import {
  NetworkMessage,
  PlayerUpdateMessage,
  PlayerJoinMessage,
  PlayerLeaveMessage,
  GameStateMessage
} from '@/types';
import { NETWORK } from '@/utils/constants';

type MessageHandler = (message: NetworkMessage) => void;

class NetworkService {
  private socket: WebSocket | null = null;
  private messageHandlers: Map<string, MessageHandler[]> = new Map();
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private isConnecting: boolean = false;

  constructor() {
    this.setupMessageHandlers();
  }

  // Connect to the game server
  async connect(): Promise<boolean> {
    if (this.isConnecting || this.isConnected()) {
      return this.isConnected();
    }

    this.isConnecting = true;

    try {
      this.socket = new WebSocket(NETWORK.SERVER_URL);

      this.socket.onopen = this.handleOpen.bind(this);
      this.socket.onmessage = this.handleMessage.bind(this);
      this.socket.onclose = this.handleClose.bind(this);
      this.socket.onerror = this.handleError.bind(this);

      // Wait for connection to establish
      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          this.isConnecting = false;
          resolve(false);
        }, 5000);

        this.socket!.onopen = () => {
          clearTimeout(timeout);
          this.handleOpen();
          this.isConnecting = false;
          resolve(true);
        };
      });
    } catch (error) {
      console.error('Failed to connect to server:', error);
      this.isConnecting = false;
      return false;
    }
  }

  // Disconnect from the server
  disconnect(): void {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }

    this.clearTimers();
    this.reconnectAttempts = 0;
  }

  // Check if connected to server
  isConnected(): boolean {
    return this.socket?.readyState === WebSocket.OPEN;
  }

  // Send a message to the server
  sendMessage(message: NetworkMessage): boolean {
    if (!this.isConnected()) {
      console.warn('Cannot send message: not connected to server');
      return false;
    }

    try {
      this.socket!.send(JSON.stringify(message));
      return true;
    } catch (error) {
      console.error('Failed to send message:', error);
      return false;
    }
  }

  // Send player update
  sendPlayerUpdate(data: PlayerUpdateMessage['data']): boolean {
    const message: PlayerUpdateMessage = {
      type: 'player_update',
      timestamp: Date.now(),
      data,
    };
    return this.sendMessage(message);
  }

  // Register message handler
  onMessage(type: string, handler: MessageHandler): void {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, []);
    }
    this.messageHandlers.get(type)!.push(handler);
  }

  // Unregister message handler
  offMessage(type: string, handler: MessageHandler): void {
    const handlers = this.messageHandlers.get(type);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  // Handle connection open
  private handleOpen(): void {
    console.log('Connected to game server');
    this.reconnectAttempts = 0;
    this.startHeartbeat();
    this.emit('connected', null);
  }

  // Handle incoming messages
  private handleMessage(event: MessageEvent): void {
    try {
      const message: NetworkMessage = JSON.parse(event.data);
      this.emit(message.type, message);
    } catch (error) {
      console.error('Failed to parse message:', error);
    }
  }

  // Handle connection close
  private handleClose(): void {
    console.log('Disconnected from game server');
    this.clearTimers();
    this.emit('disconnected', null);

    // Attempt to reconnect
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.scheduleReconnect();
    }
  }

  // Handle connection error
  private handleError(error: Event): void {
    console.error('WebSocket error:', error);
    this.emit('error', {
      type: 'error',
      timestamp: Date.now(),
      data: { error: error.type || 'Unknown error' }
    });
  }

  // Emit message to handlers
  private emit(type: string, message: NetworkMessage | null): void {
    const handlers = this.messageHandlers.get(type);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(message!);
        } catch (error) {
          console.error('Error in message handler:', error);
        }
      });
    }
  }

  // Schedule reconnection attempt
  private scheduleReconnect(): void {
    this.reconnectAttempts++;
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);

    console.log(`Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts})`);

    this.reconnectTimer = setTimeout(() => {
      this.connect();
    }, delay);
  }

  // Start heartbeat to keep connection alive
  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected()) {
        this.sendMessage({
          type: 'heartbeat',
          timestamp: Date.now(),
          data: null,
        });
      }
    }, NETWORK.HEARTBEAT_INTERVAL);
  }

  // Clear all timers
  private clearTimers(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  // Setup default message handlers
  private setupMessageHandlers(): void {
    // Handle heartbeat responses
    this.onMessage('heartbeat_response', () => {
      // Server is alive
    });
  }
}

// Export singleton instance
export const networkService = new NetworkService();
export default NetworkService;
