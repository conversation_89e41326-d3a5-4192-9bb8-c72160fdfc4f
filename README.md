# Fish Eat Fish 2 - React Native Game

A multiplayer "eat and grow" fish game built with React Native, featuring real-time gameplay, leaderboards, and smooth mobile controls.

## Features

- **Multiplayer Gameplay**: Compete with other players in real-time
- **Progressive Growth**: Eat smaller fish to grow larger and stronger
- **Boost Mechanic**: Use boost for speed at the cost of growth progress
- **Visual Indicators**: Color-coded outlines show which fish you can eat
- **Leaderboard**: Real-time ranking of all players
- **Minimap**: Navigate the large game world with ease
- **Mobile-First Design**: Optimized for touch controls with virtual joystick

## Technology Stack

- **Frontend**: React Native with TypeScript
- **Game Engine**: React Native Game Engine with Matter.js physics
- **State Management**: Zustand
- **UI Components**: React Native with Reanimated 2
- **Graphics**: React Native SVG for fish rendering
- **Navigation**: React Navigation 6

## Game Mechanics

### Movement
- Use the virtual joystick to control your fish
- Movement has inertia for smooth, realistic feel
- Fish move primarily horizontally as per design

### Eating & Growth
- Eat fish that are strictly smaller than you (green outline)
- Avoid fish that are larger (red outline)
- Fish of the same size bounce off each other (yellow outline)
- Consume food particles for small growth boosts
- Fill the progress bar to level up and increase in size

### Boost System
- Extend joystick to maximum range to activate boost
- Boost provides temporary speed increase
- Consumes growth progress while active
- Can cause you to shrink to previous levels if overused

### Visual Feedback
- Fish change size, shape, and color as they grow
- Particle effects for eating, boosting, and leveling up
- Real-time progress bar shows growth toward next level
- Minimap shows your position and nearby entities

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. For iOS:
   ```bash
   cd ios && pod install && cd ..
   npm run ios
   ```

4. For Android:
   ```bash
   npm run android
   ```

## Development

### Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── VirtualJoystick.tsx
│   ├── ProgressBar.tsx
│   ├── Leaderboard.tsx
│   └── Minimap.tsx
├── screens/            # Screen components
│   ├── MainMenuScreen.tsx
│   ├── GameScreen.tsx
│   ├── GameOverScreen.tsx
│   └── TutorialScreen.tsx
├── game/               # Game engine and logic
│   ├── engine/         # Core game engine
│   ├── entities/       # Game entities (Fish, Food)
│   └── systems/        # Game systems (Movement, Collision, etc.)
├── store/              # State management
├── types/              # TypeScript type definitions
├── utils/              # Utility functions and constants
└── services/           # External services (networking, etc.)
```

### Key Components

- **GameEngine**: Main game loop and entity management
- **MovementSystem**: Handles fish movement and AI
- **CollisionSystem**: Manages fish-to-fish and fish-to-food collisions
- **SpawningSystem**: Spawns enemies and food particles
- **CameraSystem**: Follows player and manages viewport

## Game Balance

The game is carefully balanced to provide engaging progression:

- **Size Levels**: 20 levels with increasing radius and different colors
- **Growth Requirements**: Each level requires more food than the previous
- **Speed Scaling**: Larger fish move slightly slower
- **Spawn Rates**: Enemies and food spawn at balanced intervals
- **Vision Range**: Limited visibility adds strategic depth

## Performance

- Target 60 FPS on mid-range mobile devices
- Efficient collision detection with spatial optimization
- Entity culling for off-screen objects
- Smooth animations with React Native Reanimated

## Future Enhancements

- Multiplayer server implementation
- Power-ups and special abilities
- Different game modes
- Customizable fish skins
- Achievement system
- Sound effects and music

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
