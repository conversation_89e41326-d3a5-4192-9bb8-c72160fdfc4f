{"version": 3, "names": ["useEffect", "useRef", "initialUpdaterRun", "makeMutable", "startMapper", "stopMapper", "shouldBeUseWeb", "useDerivedValue", "updater", "dependencies", "initRef", "inputs", "Object", "values", "__closure", "length", "undefined", "__workletHash", "push", "current", "sharedValue", "fun", "value", "mapperId"], "sourceRoot": "../../../src", "sources": ["hook/useDerivedValue.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAEzC,SAASC,iBAAiB,QAAQ,uBAAc;AAEhD,SAASC,WAAW,EAAEC,WAAW,EAAEC,UAAU,QAAQ,YAAS;AAC9D,SAASC,cAAc,QAAQ,uBAAoB;;AAanD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAMA,OAAO,SAASC,eAAeA,CAC7BC,OAAmC,EACnCC,YAA6B,EACR;EACrB,MAAMC,OAAO,GAAGT,MAAM,CAA4B,IAAI,CAAC;EACvD,IAAIU,MAAM,GAAGC,MAAM,CAACC,MAAM,CAACL,OAAO,CAACM,SAAS,IAAI,CAAC,CAAC,CAAC;EACnD,IAAIR,cAAc,CAAC,CAAC,EAAE;IACpB,IAAI,CAACK,MAAM,CAACI,MAAM,IAAIN,YAAY,EAAEM,MAAM,EAAE;MAC1C;MACAJ,MAAM,GAAGF,YAAY;IACvB;EACF;;EAEA;EACA,IAAIA,YAAY,KAAKO,SAAS,EAAE;IAC9BP,YAAY,GAAG,CAAC,GAAGE,MAAM,EAAEH,OAAO,CAACS,aAAa,CAAC;EACnD,CAAC,MAAM;IACLR,YAAY,CAACS,IAAI,CAACV,OAAO,CAACS,aAAa,CAAC;EAC1C;EAEA,IAAIP,OAAO,CAACS,OAAO,KAAK,IAAI,EAAE;IAC5BT,OAAO,CAACS,OAAO,GAAGhB,WAAW,CAACD,iBAAiB,CAACM,OAAO,CAAC,CAAC;EAC3D;EAEA,MAAMY,WAA+B,GAAGV,OAAO,CAACS,OAAO;EAEvDnB,SAAS,CAAC,MAAM;IACd,MAAMqB,GAAG,GAAGA,CAAA,KAAM;MAChB,SAAS;;MACTD,WAAW,CAACE,KAAK,GAAGd,OAAO,CAAC,CAAC;IAC/B,CAAC;IACD,MAAMe,QAAQ,GAAGnB,WAAW,CAACiB,GAAG,EAAEV,MAAM,EAAE,CACxCS,WAAW,CACZ,CAAC;IACF,OAAO,MAAM;MACXf,UAAU,CAACkB,QAAQ,CAAC;IACtB,CAAC;EACH,CAAC,EAAEd,YAAY,CAAC;EAEhB,OAAOW,WAAW;AACpB", "ignoreList": []}