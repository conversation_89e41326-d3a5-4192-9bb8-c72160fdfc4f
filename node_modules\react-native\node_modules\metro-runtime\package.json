{"name": "metro-runtime", "version": "0.82.4", "description": "🚇 Module required for evaluating Metro bundles.", "exports": {"./package.json": "./package.json", "./modules/asyncRequire": "./src/modules/asyncRequire.js", "./modules/empty-module": "./src/modules/empty-module.js", "./modules/HMRClient": "./src/modules/HMRClient.js", "./modules/null-module": "./src/modules/null-module.js", "./polyfills/require": "./src/polyfills/require.js", "./private/*": "./src/*.js", "./src/*.js": "./src/*.js", "./src/*": "./src/*.js"}, "repository": {"type": "git", "url": "**************:facebook/metro.git"}, "scripts": {"prepare-release": "test -d build && rm -rf src.real && mv src src.real && mv build src", "cleanup-release": "test ! -e build && mv src build && mv src.real src"}, "license": "MIT", "dependencies": {"@babel/runtime": "^7.25.0", "flow-enums-runtime": "^0.0.6"}, "devDependencies": {"@babel/core": "^7.25.2", "react": "19.1.0", "react-refresh": "^0.14.0", "react-test-renderer": "19.1.0"}, "engines": {"node": ">=18.18"}}