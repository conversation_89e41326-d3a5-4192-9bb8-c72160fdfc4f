{"version": 3, "names": ["_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "React", "Image", "Platform", "StyleSheet", "ScreenStackHeaderConfigNativeComponent", "ScreenStackHeaderSubviewNativeComponent", "ScreenStackHeaderSubview", "ScreenStackHeaderConfig", "forwardRef", "props", "ref", "createElement", "style", "styles", "headerConfig", "pointerEvents", "displayName", "ScreenStackHeaderBackButtonImage", "type", "headerSubview", "resizeMode", "fadeDuration", "ScreenStackHeaderRightView", "rest", "ScreenStackHeaderLeftView", "ScreenStackHeaderCenterView", "headerSubviewCenter", "ScreenStackHeaderSearchBarView", "create", "flexDirection", "alignItems", "justifyContent", "flexShrink", "position", "width", "OS", "undefined"], "sourceRoot": "../../../src", "sources": ["components/ScreenStackHeaderConfig.tsx"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,SAAA,WAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,CAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,CAAA,IAAAD,CAAA,OAAAE,cAAA,CAAAC,IAAA,CAAAH,CAAA,EAAAC,CAAA,MAAAL,CAAA,CAAAK,CAAA,IAAAD,CAAA,CAAAC,CAAA,aAAAL,CAAA,KAAAJ,QAAA,CAAAY,KAAA,OAAAN,SAAA;AAEb,OAAOO,KAAK,MAAM,OAAO;AAMzB,SACEC,KAAK,EAELC,QAAQ,EACRC,UAAU,QAGL,cAAc;;AAErB;AACA,OAAOC,sCAAsC,MAAM,kDAAkD;AACrG,OAAOC,uCAAuC,MAAM,mDAAmD;AAEvG,OAAO,MAAMC,wBAEZ,GAAGD,uCAA8C;AAElD,OAAO,MAAME,uBAAuB,gBAAGP,KAAK,CAACQ,UAAU,CAGrD,CAACC,KAAK,EAAEC,GAAG,kBACXV,KAAA,CAAAW,aAAA,CAACP,sCAAsC,EAAAjB,QAAA,KACjCsB,KAAK;EACTC,GAAG,EAAEA,GAAI;EACTE,KAAK,EAAEC,MAAM,CAACC,YAAa;EAC3BC,aAAa,EAAC;AAAU,EACzB,CACF,CAAC;AAEFR,uBAAuB,CAACS,WAAW,GAAG,yBAAyB;AAE/D,OAAO,MAAMC,gCAAgC,GAC3CR,KAAiB,iBAEjBT,KAAA,CAAAW,aAAA,CAACL,wBAAwB;EAACY,IAAI,EAAC,MAAM;EAACN,KAAK,EAAEC,MAAM,CAACM;AAAc,gBAChEnB,KAAA,CAAAW,aAAA,CAACV,KAAK,EAAAd,QAAA;EAACiC,UAAU,EAAC,QAAQ;EAACC,YAAY,EAAE;AAAE,GAAKZ,KAAK,CAAG,CAChC,CAC3B;AAED,OAAO,MAAMa,0BAA0B,GACrCb,KAAyC,IACzB;EAChB,MAAM;IAAEG,KAAK;IAAE,GAAGW;EAAK,CAAC,GAAGd,KAAK;EAEhC,oBACET,KAAA,CAAAW,aAAA,CAACL,wBAAwB,EAAAnB,QAAA,KACnBoC,IAAI;IACRL,IAAI,EAAC,OAAO;IACZN,KAAK,EAAE,CAACC,MAAM,CAACM,aAAa,EAAEP,KAAK;EAAE,EACtC,CAAC;AAEN,CAAC;AAED,OAAO,MAAMY,yBAAyB,GACpCf,KAAyC,IACzB;EAChB,MAAM;IAAEG,KAAK;IAAE,GAAGW;EAAK,CAAC,GAAGd,KAAK;EAEhC,oBACET,KAAA,CAAAW,aAAA,CAACL,wBAAwB,EAAAnB,QAAA,KACnBoC,IAAI;IACRL,IAAI,EAAC,MAAM;IACXN,KAAK,EAAE,CAACC,MAAM,CAACM,aAAa,EAAEP,KAAK;EAAE,EACtC,CAAC;AAEN,CAAC;AAED,OAAO,MAAMa,2BAA2B,GACtChB,KAAyC,IACzB;EAChB,MAAM;IAAEG,KAAK;IAAE,GAAGW;EAAK,CAAC,GAAGd,KAAK;EAEhC,oBACET,KAAA,CAAAW,aAAA,CAACL,wBAAwB,EAAAnB,QAAA,KACnBoC,IAAI;IACRL,IAAI,EAAC,QAAQ;IACbN,KAAK,EAAE,CAACC,MAAM,CAACa,mBAAmB,EAAEd,KAAK;EAAE,EAC5C,CAAC;AAEN,CAAC;AAED,OAAO,MAAMe,8BAA8B,GACzClB,KAA8C,iBAE9CT,KAAA,CAAAW,aAAA,CAACL,wBAAwB,EAAAnB,QAAA,KACnBsB,KAAK;EACTS,IAAI,EAAC,WAAW;EAChBN,KAAK,EAAEC,MAAM,CAACM;AAAc,EAC7B,CACF;AAED,MAAMN,MAAM,GAAGV,UAAU,CAACyB,MAAM,CAAC;EAC/BT,aAAa,EAAE;IACbU,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDL,mBAAmB,EAAE;IACnBG,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd,CAAC;EACDlB,YAAY,EAAE;IACZmB,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,MAAM;IACbL,aAAa,EAAE,KAAK;IACpBE,cAAc,EAAE,eAAe;IAC/B;IACA;IACAD,UAAU,EAAE5B,QAAQ,CAACiC,EAAE,KAAK,KAAK,GAAG,QAAQ,GAAGC;EACjD;AACF,CAAC,CAAC", "ignoreList": []}