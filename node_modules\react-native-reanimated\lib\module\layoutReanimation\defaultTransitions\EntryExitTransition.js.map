{"version": 3, "names": ["withSequence", "withTiming", "logger", "BaseAnimationBuilder", "FadeIn", "FadeOut", "EntryExitTransition", "presetName", "enteringV", "exitingV", "createInstance", "entering", "animation", "instance", "exiting", "build", "delayFunction", "getDelayFunction", "callback", "callbackV", "delay", "get<PERSON>elay", "enteringAnimation", "exitingAnimation", "exitingDuration", "getDuration", "values", "enteringValues", "exitingValues", "animations", "transform", "prop", "Object", "keys", "Array", "isArray", "for<PERSON>ach", "value", "index", "transformProp", "push", "initialValues", "duration", "sequence", "undefined", "includes", "mergedTransform", "concat", "map", "objectKeys", "length", "error", "current", "originX", "currentOriginX", "originY", "currentOriginY", "width", "currentWidth", "height", "currentHeight", "targetOriginX", "targetOriginY", "targetWidth", "targetHeight", "combineTransition"], "sourceRoot": "../../../../src", "sources": ["layoutReanimation/defaultTransitions/EntryExitTransition.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,YAAY,EAAEC,UAAU,QAAQ,0BAAiB;AAU1D,SAASC,MAAM,QAAQ,uBAAc;AACrC,SAASC,oBAAoB,QAAQ,8BAAqB;AAC1D,SAASC,MAAM,EAAEC,OAAO,QAAQ,8BAA2B;AAE3D,OAAO,MAAMC,mBAAmB,SACtBH,oBAAoB,CAE9B;EACE,OAAOI,UAAU,GAAG,qBAAqB;EAEzCC,SAAS,GAAuDJ,MAAM;EAEtEK,QAAQ,GAAuDJ,OAAO;EAEtE,OAAOK,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIJ,mBAAmB,CAAC,CAAC;EAClC;EAEA,OAAOK,QAAQA,CACbC,SAA6D,EACxC;IACrB,MAAMC,QAAQ,GAAG,IAAI,CAACH,cAAc,CAAC,CAAC;IACtC,OAAOG,QAAQ,CAACF,QAAQ,CAACC,SAAS,CAAC;EACrC;EAEAD,QAAQA,CACNC,SAA6D,EACxC;IACrB,IAAI,CAACJ,SAAS,GAAGI,SAAS;IAC1B,OAAO,IAAI;EACb;EAEA,OAAOE,OAAOA,CACZF,SAA6D,EACxC;IACrB,MAAMC,QAAQ,GAAG,IAAI,CAACH,cAAc,CAAC,CAAC;IACtC,OAAOG,QAAQ,CAACC,OAAO,CAACF,SAAS,CAAC;EACpC;EAEAE,OAAOA,CACLF,SAA6D,EACxC;IACrB,IAAI,CAACH,QAAQ,GAAGG,SAAS;IACzB,OAAO,IAAI;EACb;EAEAG,KAAK,GAAGA,CAAA,KAA+B;IACrC,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B;IACA,MAAMC,iBAAiB,GAAG,IAAI,CAACd,SAAS,CAACO,KAAK,CAAC,CAAC;IAChD;IACA,MAAMQ,gBAAgB,GAAG,IAAI,CAACd,QAAQ,CAACM,KAAK,CAAC,CAAC;IAC9C,MAAMS,eAAe,GAAG,IAAI,CAACf,QAAQ,CAACgB,WAAW,CAAC,CAAC;IAEnD,OAAQC,MAAM,IAAK;MACjB,SAAS;;MACT,MAAMC,cAAc,GAAGL,iBAAiB,CAACI,MAAM,CAAC;MAChD,MAAME,aAAa,GAAGL,gBAAgB,CAACG,MAAM,CAAC;MAC9C,MAAMG,UAAwC,GAAG;QAC/CC,SAAS,EAAE;MACb,CAAC;MAED,KAAK,MAAMC,IAAI,IAAIC,MAAM,CAACC,IAAI,CAACL,aAAa,CAACC,UAAU,CAAC,EAAE;QACxD,IAAIE,IAAI,KAAK,WAAW,EAAE;UACxB,IAAI,CAACG,KAAK,CAACC,OAAO,CAACP,aAAa,CAACC,UAAU,CAACC,SAAS,CAAC,EAAE;YACtD;UACF;UACAF,aAAa,CAACC,UAAU,CAACC,SAAS,CAACM,OAAO,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;YAC3D,KAAK,MAAMC,aAAa,IAAIP,MAAM,CAACC,IAAI,CAACI,KAAK,CAAC,EAAE;cAC9CR,UAAU,CAACC,SAAS,CAAEU,IAAI,CAAC;gBACzB,CAACD,aAAa,GAAGvB,aAAa,CAC5BI,KAAK,EACLpB,YAAY,CACVqC,KAAK,CAACE,aAAa,CAA6B,EAChDtC,UAAU,CACR2B,aAAa,CAACa,aAAa,CAACX,SAAS;gBACjC;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACAF,aAAa,CAACa,aAAa,CAACX,SAAS,CAACQ,KAAK,CAAC,CAC1CC,aAAa,CACd,GACD,CAAC,EACL;kBAAEG,QAAQ,EAAE;gBAAE,CAChB,CACF,CACF;cACF,CAAuB,CAAC;YAC1B;UACF,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,MAAMC,QAAQ,GACZhB,cAAc,CAACE,UAAU,CAACE,IAAI,CAAC,KAAKa,SAAS,GACzC,CACEhB,aAAa,CAACC,UAAU,CAACE,IAAI,CAAC,EAC9B9B,UAAU,CAAC0B,cAAc,CAACc,aAAa,CAACV,IAAI,CAAC,EAAE;YAC7CW,QAAQ,EAAE;UACZ,CAAC,CAAC,EACFf,cAAc,CAACE,UAAU,CAACE,IAAI,CAAC,CAChC,GACD,CACEH,aAAa,CAACC,UAAU,CAACE,IAAI,CAAC,EAC9B9B,UAAU,CACR+B,MAAM,CAACC,IAAI,CAACP,MAAM,CAAC,CAACmB,QAAQ,CAACd,IAAI,CAAC,GAC9BL,MAAM,CAACK,IAAI,CAAiC,GAC5CH,aAAa,CAACa,aAAa,CAACV,IAAI,CAAC,EACrC;YAAEW,QAAQ,EAAE;UAAE,CAChB,CAAC,CACF;UAEPb,UAAU,CAACE,IAAI,CAAC,GAAGf,aAAa,CAACI,KAAK,EAAEpB,YAAY,CAAC,GAAG2C,QAAQ,CAAC,CAAC;QACpE;MACF;MACA,KAAK,MAAMZ,IAAI,IAAIC,MAAM,CAACC,IAAI,CAACN,cAAc,CAACE,UAAU,CAAC,EAAE;QACzD,IAAIE,IAAI,KAAK,WAAW,EAAE;UACxB,IAAI,CAACG,KAAK,CAACC,OAAO,CAACR,cAAc,CAACE,UAAU,CAACC,SAAS,CAAC,EAAE;YACvD;UACF;UACAH,cAAc,CAACE,UAAU,CAACC,SAAS,CAACM,OAAO,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;YAC5D,KAAK,MAAMC,aAAa,IAAIP,MAAM,CAACC,IAAI,CAACI,KAAK,CAAC,EAAE;cAC9CR,UAAU,CAACC,SAAS,CAAEU,IAAI,CAAC;gBACzB,CAACD,aAAa,GAAGvB,aAAa,CAC5BI,KAAK,GAAGI,eAAe,EACvBxB,YAAY,CACVC,UAAU,CACR0B,cAAc,CAACc,aAAa,CAACX,SAAS,GAEhCH,cAAc,CAACc,aAAa,CACzBX,SAAS,CACZQ,KAAK,CAAC,CACNC,aAAa,CACd,GACD,CAAC,EACL;kBAAEG,QAAQ,EAAElB;gBAAgB,CAC9B,CAAC,EACDa,KAAK,CACHE,aAAa,CAEjB,CACF;cACF,CAAuB,CAAC;YAC1B;UACF,CAAC,CAAC;QACJ,CAAC,MAAM,IAAIV,UAAU,CAACE,IAAI,CAAC,KAAKa,SAAS,EAAE;UACzC;UACA;QACF,CAAC,MAAM;UACLf,UAAU,CAACE,IAAI,CAAC,GAAGf,aAAa,CAC9BI,KAAK,EACLpB,YAAY,CACVC,UAAU,CAAC0B,cAAc,CAACc,aAAa,CAACV,IAAI,CAAC,EAAE;YAAEW,QAAQ,EAAE;UAAE,CAAC,CAAC,EAC/Df,cAAc,CAACE,UAAU,CAACE,IAAI,CAChC,CACF,CAAC;QACH;MACF;MAEA,MAAMe,eAAe,GAAG,CACtBZ,KAAK,CAACC,OAAO,CAACP,aAAa,CAACa,aAAa,CAACX,SAAS,CAAC,GAChDF,aAAa,CAACa,aAAa,CAACX,SAAS,GACrC,EAAE,EACNiB,MAAM,CACN,CAACb,KAAK,CAACC,OAAO,CAACR,cAAc,CAACE,UAAU,CAACC,SAAS,CAAC,GAC/CH,cAAc,CAACE,UAAU,CAACC,SAAS,GACnC,EAAE,EACJkB,GAAG,CAAEX,KAAK,IAAK;QACf,MAAMY,UAAU,GAAGjB,MAAM,CAACC,IAAI,CAACI,KAAK,CAAC;QACrC,IAAIY,UAAU,EAAEC,MAAM,GAAG,CAAC,EAAE;UAC1BhD,MAAM,CAACiD,KAAK,CAAC,2CAA2C,CAAC;UACzD,OAAOd,KAAK;QACd;QAEA,MAAME,aAAa,GAAGU,UAAU,CAAC,CAAC,CAAC;QACnC,MAAMG,OAAO;QACX;QACA;QACCf,KAAK,CAACE,aAAa,CAAC,CAAqBa,OAAO;QACnD,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;UAC/B,IAAIA,OAAO,CAACP,QAAQ,CAAC,KAAK,CAAC,EAAE;YAC3B,OAAO;cACL,CAACN,aAAa,GAAG;YACnB,CAAC;UACH,CAAC,MAAM;YACL,OAAO;cACL,CAACA,aAAa,GAAG;YACnB,CAAC;UACH;QACF,CAAC,MAAM,IAAIA,aAAa,CAACM,QAAQ,CAAC,WAAW,CAAC,EAAE;UAC9C,OAAO;YAAE,CAACN,aAAa,GAAG;UAAE,CAAC;QAC/B,CAAC,MAAM;UACL,OAAO;YAAE,CAACA,aAAa,GAAG;UAAE,CAAC;QAC/B;MACF,CAAC,CACH,CAAC;MAED,OAAO;QACLE,aAAa,EAAE;UACb,GAAGb,aAAa,CAACa,aAAa;UAC9BY,OAAO,EAAE3B,MAAM,CAAC4B,cAAc;UAC9BC,OAAO,EAAE7B,MAAM,CAAC8B,cAAc;UAC9BC,KAAK,EAAE/B,MAAM,CAACgC,YAAY;UAC1BC,MAAM,EAAEjC,MAAM,CAACkC,aAAa;UAC5B9B,SAAS,EAAEgB;QACb,CAAC;QACDjB,UAAU,EAAE;UACVwB,OAAO,EAAErC,aAAa,CACpBI,KAAK,GAAGI,eAAe,EACvBvB,UAAU,CAACyB,MAAM,CAACmC,aAAa,EAAE;YAAEnB,QAAQ,EAAElB;UAAgB,CAAC,CAChE,CAAC;UACD+B,OAAO,EAAEvC,aAAa,CACpBI,KAAK,GAAGI,eAAe,EACvBvB,UAAU,CAACyB,MAAM,CAACoC,aAAa,EAAE;YAAEpB,QAAQ,EAAElB;UAAgB,CAAC,CAChE,CAAC;UACDiC,KAAK,EAAEzC,aAAa,CAClBI,KAAK,GAAGI,eAAe,EACvBvB,UAAU,CAACyB,MAAM,CAACqC,WAAW,EAAE;YAAErB,QAAQ,EAAElB;UAAgB,CAAC,CAC9D,CAAC;UACDmC,MAAM,EAAE3C,aAAa,CACnBI,KAAK,GAAGI,eAAe,EACvBvB,UAAU,CAACyB,MAAM,CAACsC,YAAY,EAAE;YAAEtB,QAAQ,EAAElB;UAAgB,CAAC,CAC/D,CAAC;UACD,GAAGK;QACL,CAAC;QACDX;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS+C,iBAAiBA,CAC/BnD,OAA2D,EAC3DH,QAA4D,EACvC;EACrB,OAAOL,mBAAmB,CAACK,QAAQ,CAACA,QAAQ,CAAC,CAACG,OAAO,CAACA,OAAO,CAAC;AAChE", "ignoreList": []}