{"version": 3, "names": ["withTiming", "getReduceMotionFromConfig", "LayoutAnimationType", "ReduceMotion", "SharedTransitionType", "ReanimatedError", "updateLayoutAnimations", "ProgressTransitionManager", "SUPPORTED_PROPS", "SharedTransition", "_customAnimationFactory", "_animation", "_transitionDuration", "_reduceMotion", "System", "_customProgressAnimation", "undefined", "_progressAnimation", "_defaultTransitionType", "_progressTransitionManager", "custom", "customAnimationFactory", "progressAnimation", "progressAnimationCallback", "viewTag", "values", "progress", "newStyles", "global", "_notifyAboutProgress", "duration", "reduceMotion", "defaultTransitionType", "transitionType", "registerTransition", "sharedTransitionTag", "isUnmounting", "getReduceMotion", "transitionAnimation", "getTransitionAnimation", "getProgressAnimation", "ANIMATION", "PROGRESS_ANIMATION", "layoutAnimationType", "SHARED_ELEMENT_TRANSITION", "SHARED_ELEMENT_TRANSITION_PROGRESS", "addProgressAnimation", "unregisterTransition", "removeProgressAnimation", "buildAnimation", "buildProgressAnimation", "animationFactory", "transitionDuration", "animations", "initialValues", "key", "includes", "propName", "matrix", "targetTransformMatrix", "transformMatrix", "capitalizedPropName", "char<PERSON>t", "toUpperCase", "slice", "keyToTargetValue", "currentTransformMatrix", "keyToCurrentValue", "propertyName", "currentMatrix", "targetMatrix", "newMatrix", "Array", "i", "PropertyName", "currentPropertyName", "targetPropertyName", "currentValue", "targetValue"], "sourceRoot": "../../../../src", "sources": ["layoutReanimation/sharedTransitions/SharedTransition.ts"], "mappings": "AAAA,YAAY;;AACZ,SAASA,UAAU,QAAQ,0BAAiB;AAC5C,SAASC,yBAAyB,QAAQ,yBAAsB;AAShE,SACEC,mBAAmB,EACnBC,YAAY,EACZC,oBAAoB,QACf,sBAAmB;AAC1B,SAASC,eAAe,QAAQ,iBAAc;AAC9C,SAASC,sBAAsB,QAAQ,iCAA8B;AACrE,SAASC,yBAAyB,QAAQ,gCAA6B;AAEvE,MAAMC,eAAe,GAAG,CACtB,OAAO,EACP,QAAQ,EACR,SAAS,EACT,SAAS,EACT,WAAW,EACX,cAAc,EACd,qBAAqB,EACrB,sBAAsB,EACtB,wBAAwB,EACxB,yBAAyB,CACjB;AAMV;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,gBAAgB,CAAC;EACpBC,uBAAuB,GAA4B,IAAI;EACvDC,UAAU,GAA8C,IAAI;EAC5DC,mBAAmB,GAAG,GAAG;EACzBC,aAAa,GAAiBV,YAAY,CAACW,MAAM;EACjDC,wBAAwB,GAAuBC,SAAS;EACxDC,kBAAkB,GAAuBD,SAAS;EAClDE,sBAAsB,GAA0BF,SAAS;EACjE,OAAeG,0BAA0B,GAAG,IAAIZ,yBAAyB,CAAC,CAAC;EAEpEa,MAAMA,CAACC,sBAAwC,EAAoB;IACxE,IAAI,CAACX,uBAAuB,GAAGW,sBAAsB;IACrD,OAAO,IAAI;EACb;EAEOC,iBAAiBA,CACtBC,yBAAkD,EAChC;IAClB,IAAI,CAACR,wBAAwB,GAAG,CAACS,OAAO,EAAEC,MAAM,EAAEC,QAAQ,KAAK;MAC7D,SAAS;;MACT,MAAMC,SAAS,GAAGJ,yBAAyB,CAACE,MAAM,EAAEC,QAAQ,CAAC;MAC7DE,MAAM,CAACC,oBAAoB,CAACL,OAAO,EAAEG,SAAS,EAAE,IAAI,CAAC;IACvD,CAAC;IACD,OAAO,IAAI;EACb;EAEOG,QAAQA,CAACA,QAAgB,EAAoB;IAClD,IAAI,CAAClB,mBAAmB,GAAGkB,QAAQ;IACnC,OAAO,IAAI;EACb;EAEOC,YAAYA,CAAClB,aAA2B,EAAQ;IACrD,IAAI,CAACA,aAAa,GAAGA,aAAa;IAClC,OAAO,IAAI;EACb;EAEOmB,qBAAqBA,CAC1BC,cAAoC,EAClB;IAClB,IAAI,CAACf,sBAAsB,GAAGe,cAAc;IAC5C,OAAO,IAAI;EACb;EAEOC,kBAAkBA,CACvBV,OAAe,EACfW,mBAA2B,EAC3BC,YAAY,GAAG,KAAK,EACpB;IACA,IAAInC,yBAAyB,CAAC,IAAI,CAACoC,eAAe,CAAC,CAAC,CAAC,EAAE;MACrD;IACF;IAEA,MAAMC,mBAAmB,GAAG,IAAI,CAACC,sBAAsB,CAAC,CAAC;IACzD,MAAMjB,iBAAiB,GAAG,IAAI,CAACkB,oBAAoB,CAAC,CAAC;IACrD,IAAI,CAAC,IAAI,CAACtB,sBAAsB,EAAE;MAChC,IAAI,IAAI,CAACR,uBAAuB,IAAI,CAAC,IAAI,CAACK,wBAAwB,EAAE;QAClE,IAAI,CAACG,sBAAsB,GAAGd,oBAAoB,CAACqC,SAAS;MAC9D,CAAC,MAAM;QACL,IAAI,CAACvB,sBAAsB,GAAGd,oBAAoB,CAACsC,kBAAkB;MACvE;IACF;IACA,MAAMC,mBAAmB,GACvB,IAAI,CAACzB,sBAAsB,KAAKd,oBAAoB,CAACqC,SAAS,GAC1DvC,mBAAmB,CAAC0C,yBAAyB,GAC7C1C,mBAAmB,CAAC2C,kCAAkC;IAC5DvC,sBAAsB,CACpBkB,OAAO,EACPmB,mBAAmB,EACnBL,mBAAmB,EACnBH,mBAAmB,EACnBC,YACF,CAAC;IACD3B,gBAAgB,CAACU,0BAA0B,CAAC2B,oBAAoB,CAC9DtB,OAAO,EACPF,iBACF,CAAC;EACH;EAEOyB,oBAAoBA,CAACvB,OAAe,EAAEY,YAAY,GAAG,KAAK,EAAQ;IACvE,MAAMO,mBAAmB,GACvB,IAAI,CAACzB,sBAAsB,KAAKd,oBAAoB,CAACqC,SAAS,GAC1DvC,mBAAmB,CAAC0C,yBAAyB,GAC7C1C,mBAAmB,CAAC2C,kCAAkC;IAC5DvC,sBAAsB,CACpBkB,OAAO,EACPmB,mBAAmB,EACnB3B,SAAS,EACTA,SAAS,EACToB,YACF,CAAC;IACD3B,gBAAgB,CAACU,0BAA0B,CAAC6B,uBAAuB,CACjExB,OAAO,EACPY,YACF,CAAC;EACH;EAEOC,eAAeA,CAAA,EAAiB;IACrC,OAAO,IAAI,CAACxB,aAAa;EAC3B;EAEQ0B,sBAAsBA,CAAA,EAAuC;IACnE,IAAI,CAAC,IAAI,CAAC5B,UAAU,EAAE;MACpB,IAAI,CAACsC,cAAc,CAAC,CAAC;IACvB;IACA,OAAO,IAAI,CAACtC,UAAU;EACxB;EAEQ6B,oBAAoBA,CAAA,EAAsB;IAChD,IAAI,CAAC,IAAI,CAACvB,kBAAkB,EAAE;MAC5B,IAAI,CAACiC,sBAAsB,CAAC,CAAC;IAC/B;IACA,OAAO,IAAI,CAACjC,kBAAkB;EAChC;EAEQgC,cAAcA,CAAA,EAAG;IACvB,MAAME,gBAAgB,GAAG,IAAI,CAACzC,uBAAuB;IACrD,MAAM0C,kBAAkB,GAAG,IAAI,CAACxC,mBAAmB;IACnD,MAAMmB,YAAY,GAAG,IAAI,CAAClB,aAAa;IACvC,IAAI,CAACF,UAAU,GAAIc,MAAwC,IAAK;MAC9D,SAAS;;MACT,IAAI4B,UAEH,GAAG,CAAC,CAAC;MACN,MAAMC,aAEL,GAAG,CAAC,CAAC;MAEN,IAAIH,gBAAgB,EAAE;QACpBE,UAAU,GAAGF,gBAAgB,CAAC1B,MAAM,CAAC;QACrC,KAAK,MAAM8B,GAAG,IAAIF,UAAU,EAAE;UAC5B,IAAI,CAAE7C,eAAe,CAAuBgD,QAAQ,CAACD,GAAG,CAAC,EAAE;YACzD,MAAM,IAAIlD,eAAe,CACvB,aAAakD,GAAG,yBAClB,CAAC;UACH;QACF;MACF,CAAC,MAAM;QACL,KAAK,MAAME,QAAQ,IAAIjD,eAAe,EAAE;UACtC,IAAIiD,QAAQ,KAAK,WAAW,EAAE;YAC5B,MAAMC,MAAM,GAAGjC,MAAM,CAACkC,qBAAqB;YAC3CN,UAAU,CAACO,eAAe,GAAG5D,UAAU,CAAC0D,MAAM,EAAE;cAC9C3B,YAAY;cACZD,QAAQ,EAAEsB;YACZ,CAAC,CAAC;UACJ,CAAC,MAAM;YACL,MAAMS,mBAAmB,GAAG,GAAGJ,QAAQ,CACpCK,MAAM,CAAC,CAAC,CAAC,CACTC,WAAW,CAAC,CAAC,GAAGN,QAAQ,CAACO,KAAK,CAC/B,CACF,CAAC,EAAyC;YAC1C,MAAMC,gBAAgB,GAAG,SAASJ,mBAAmB,EAAW;YAChER,UAAU,CAACI,QAAQ,CAAC,GAAGzD,UAAU,CAACyB,MAAM,CAACwC,gBAAgB,CAAC,EAAE;cAC1DlC,YAAY;cACZD,QAAQ,EAAEsB;YACZ,CAAC,CAAC;UACJ;QACF;MACF;MAEA,KAAK,MAAMK,QAAQ,IAAIJ,UAAU,EAAE;QACjC,IAAII,QAAQ,KAAK,WAAW,EAAE;UAC5BH,aAAa,CAACM,eAAe,GAAGnC,MAAM,CAACyC,sBAAsB;QAC/D,CAAC,MAAM;UACL,MAAML,mBAAmB,GAAIJ,QAAQ,CAACK,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAC3DN,QAAQ,CAACO,KAAK,CAAC,CAAC,CAAyC;UAC3D,MAAMG,iBAAiB,GAAG,UAAUN,mBAAmB,EAAW;UAClEP,aAAa,CAACG,QAAQ,CAAC,GAAGhC,MAAM,CAAC0C,iBAAiB,CAAC;QACrD;MACF;MAEA,OAAO;QAAEb,aAAa;QAAED;MAAW,CAAC;IACtC,CAAC;EACH;EAEQH,sBAAsBA,CAAA,EAAG;IAC/B,IAAI,IAAI,CAACnC,wBAAwB,EAAE;MACjC,IAAI,CAACE,kBAAkB,GAAG,IAAI,CAACF,wBAAwB;MACvD;IACF;IACA,IAAI,CAACE,kBAAkB,GAAG,CAACO,OAAO,EAAEC,MAAM,EAAEC,QAAQ,KAAK;MACvD,SAAS;;MACT,MAAMC,SAA+C,GAAG,CAAC,CAAC;MAC1D,KAAK,MAAMyC,YAAY,IAAI5D,eAAe,EAAE;QAC1C,IAAI4D,YAAY,KAAK,WAAW,EAAE;UAChC;UACA;UACA,MAAMC,aAAa,GAAG5C,MAAM,CAACyC,sBAAsB;UACnD,MAAMI,YAAY,GAAG7C,MAAM,CAACkC,qBAAqB;UACjD,MAAMY,SAAS,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC;UAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;YAC1BF,SAAS,CAACE,CAAC,CAAC,GACV/C,QAAQ,IAAI4C,YAAY,CAACG,CAAC,CAAC,GAAGJ,aAAa,CAACI,CAAC,CAAC,CAAC,GAC/CJ,aAAa,CAACI,CAAC,CAAC;UACpB;UACA9C,SAAS,CAACiC,eAAe,GAAGW,SAAS;QACvC,CAAC,MAAM;UACL;UACA,MAAMG,YAAY,GAAIN,YAAY,CAACN,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GACxDK,YAAY,CAACJ,KAAK,CAAC,CAAC,CAAyC;UAC/D,MAAMW,mBAAmB,GAAG,UAAUD,YAAY,EAAW;UAC7D,MAAME,kBAAkB,GAAG,SAASF,YAAY,EAAW;UAE3D,MAAMG,YAAY,GAAGpD,MAAM,CAACkD,mBAAmB,CAAC;UAChD,MAAMG,WAAW,GAAGrD,MAAM,CAACmD,kBAAkB,CAAC;UAE9CjD,SAAS,CAACyC,YAAY,CAAC,GACrB1C,QAAQ,IAAIoD,WAAW,GAAGD,YAAY,CAAC,GAAGA,YAAY;QAC1D;MACF;MACAjD,MAAM,CAACC,oBAAoB,CAACL,OAAO,EAAEG,SAAS,EAAE,IAAI,CAAC;IACvD,CAAC;EACH;;EAEA;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAcP,MAAMA,CAClBC,sBAAwC,EACtB;IAClB,OAAO,IAAIZ,gBAAgB,CAAC,CAAC,CAACW,MAAM,CAACC,sBAAsB,CAAC;EAC9D;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAcS,QAAQA,CAACA,QAAgB,EAAoB;IACzD,OAAO,IAAIrB,gBAAgB,CAAC,CAAC,CAACqB,QAAQ,CAACA,QAAQ,CAAC;EAClD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAcR,iBAAiBA,CAC7BC,yBAAkD,EAChC;IAClB,OAAO,IAAId,gBAAgB,CAAC,CAAC,CAACa,iBAAiB,CAACC,yBAAyB,CAAC;EAC5E;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAcS,qBAAqBA,CACjCC,cAAoC,EAClB;IAClB,OAAO,IAAIxB,gBAAgB,CAAC,CAAC,CAACuB,qBAAqB,CAACC,cAAc,CAAC;EACrE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,OAAcF,YAAYA,CAACA,YAA0B,EAAoB;IACvE,OAAO,IAAItB,gBAAgB,CAAC,CAAC,CAACsB,YAAY,CAACA,YAAY,CAAC;EAC1D;AACF", "ignoreList": []}