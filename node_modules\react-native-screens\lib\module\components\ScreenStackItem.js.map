{"version": 3, "names": ["React", "Platform", "StyleSheet", "warnOnce", "DebugContainer", "ScreenStackHeaderConfig", "Screen", "ScreenStack", "RNSScreensRefContext", "FooterComponent", "ScreenStackItem", "children", "headerConfig", "activityState", "shouldFreeze", "stackPresentation", "sheetAllowedDetents", "contentStyle", "style", "screenId", "unstable_sheetFooter", "rest", "ref", "currentScreenRef", "useRef", "screenRefs", "useContext", "useImperativeHandle", "current", "isHeaderInModal", "OS", "hidden", "headerHiddenPreviousRef", "useEffect", "content", "createElement", "Fragment", "styles", "absolute", "container", "internalScreenStyle", "flattenContentStyles", "flatten", "backgroundColor", "_extends", "node", "console", "warn", "currentRefs", "enabled", "isNativeStack", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "largeTitle", "absoluteFill", "forwardRef", "create", "flex", "position", "top", "start", "end"], "sourceRoot": "../../../src", "sources": ["components/ScreenStackItem.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,QAAQ,EAERC,UAAU,QAGL,cAAc;AACrB,OAAOC,QAAQ,MAAM,WAAW;AAEhC,OAAOC,cAAc,MAAM,kBAAkB;AAE7C,SAASC,uBAAuB,QAAQ,2BAA2B;AACnE,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,oBAAoB,QAAQ,aAAa;AAClD,SAASC,eAAe,QAAQ,gBAAgB;AAWhD,SAASC,eAAeA,CACtB;EACEC,QAAQ;EACRC,YAAY;EACZC,aAAa;EACbC,YAAY;EACZC,iBAAiB;EACjBC,mBAAmB;EACnBC,YAAY;EACZC,KAAK;EACLC,QAAQ;EACR;EACAC,oBAAoB;EACpB,GAAGC;AACE,CAAC,EACRC,GAA6B,EAC7B;EACA,MAAMC,gBAAgB,GAAGvB,KAAK,CAACwB,MAAM,CAAc,IAAI,CAAC;EACxD,MAAMC,UAAU,GAAGzB,KAAK,CAAC0B,UAAU,CAAClB,oBAAoB,CAAC;EAEzDR,KAAK,CAAC2B,mBAAmB,CAACL,GAAG,EAAE,MAAMC,gBAAgB,CAACK,OAAQ,CAAC;EAE/D,MAAMC,eAAe,GACnB5B,QAAQ,CAAC6B,EAAE,KAAK,SAAS,GACrB,KAAK,GACLf,iBAAiB,KAAK,MAAM,IAAIH,YAAY,EAAEmB,MAAM,KAAK,KAAK;EAEpE,MAAMC,uBAAuB,GAAGhC,KAAK,CAACwB,MAAM,CAACZ,YAAY,EAAEmB,MAAM,CAAC;EAElE/B,KAAK,CAACiC,SAAS,CAAC,MAAM;IACpB9B,QAAQ,CACNF,QAAQ,CAAC6B,EAAE,KAAK,SAAS,IACvBf,iBAAiB,KAAK,MAAM,IAC5BiB,uBAAuB,CAACJ,OAAO,KAAKhB,YAAY,EAAEmB,MAAM,EAC1D,qHACF,CAAC;IAEDC,uBAAuB,CAACJ,OAAO,GAAGhB,YAAY,EAAEmB,MAAM;EACxD,CAAC,EAAE,CAACnB,YAAY,EAAEmB,MAAM,EAAEhB,iBAAiB,CAAC,CAAC;EAE7C,MAAMmB,OAAO,gBACXlC,KAAA,CAAAmC,aAAA,CAAAnC,KAAA,CAAAoC,QAAA,qBACEpC,KAAA,CAAAmC,aAAA,CAAC/B,cAAc;IACbc,KAAK,EAAE,CACLH,iBAAiB,KAAK,WAAW,GAC7Bd,QAAQ,CAAC6B,EAAE,KAAK,KAAK,GACnBO,MAAM,CAACC,QAAQ,GACftB,mBAAmB,KAAK,eAAe,GACvC,IAAI,GACJqB,MAAM,CAACE,SAAS,GAClBF,MAAM,CAACE,SAAS,EACpBtB,YAAY,CACZ;IACFF,iBAAiB,EAAEA,iBAAiB,IAAI;EAAO,GAC9CJ,QACa,CAAC,eAYjBX,KAAA,CAAAmC,aAAA,CAAC9B,uBAAuB,EAAKO,YAAe,CAAC,EAE5CG,iBAAiB,KAAK,WAAW,IAAIK,oBAAoB,iBACxDpB,KAAA,CAAAmC,aAAA,CAAC1B,eAAe,QAAEW,oBAAoB,CAAC,CAAmB,CAE5D,CACH;;EAED;EACA;EACA;EACA,IAAIoB,mBAAmB;EAEvB,IAAIzB,iBAAiB,KAAK,WAAW,IAAIE,YAAY,EAAE;IACrD,MAAMwB,oBAAoB,GAAGvC,UAAU,CAACwC,OAAO,CAACzB,YAAY,CAAC;IAC7DuB,mBAAmB,GAAG;MACpBG,eAAe,EAAEF,oBAAoB,EAAEE;IACzC,CAAC;EACH;EAEA,oBACE3C,KAAA,CAAAmC,aAAA,CAAC7B,MAAM,EAAAsC,QAAA;IACLtB,GAAG,EAAEuB,IAAI,IAAI;MACXtB,gBAAgB,CAACK,OAAO,GAAGiB,IAAI;MAE/B,IAAIpB,UAAU,KAAK,IAAI,EAAE;QACvBqB,OAAO,CAACC,IAAI,CACV,kGACF,CAAC;QACD;MACF;MAEA,MAAMC,WAAW,GAAGvB,UAAU,CAACG,OAAO;MAEtC,IAAIiB,IAAI,KAAK,IAAI,EAAE;QACjB;QACA,OAAOG,WAAW,CAAC7B,QAAQ,CAAC;MAC9B,CAAC,MAAM;QACL6B,WAAW,CAAC7B,QAAQ,CAAC,GAAG;UAAES,OAAO,EAAEiB;QAAK,CAAC;MAC3C;IACF,CAAE;IACFI,OAAO;IACPC,aAAa;IACbrC,aAAa,EAAEA,aAAc;IAC7BC,YAAY,EAAEA,YAAa;IAC3BC,iBAAiB,EAAEA,iBAAkB;IACrCoC,cAAc,EAAEvC,YAAY,EAAEwC,UAAU,IAAI,KAAM;IAClDpC,mBAAmB,EAAEA,mBAAoB;IACzCE,KAAK,EAAE,CAACA,KAAK,EAAEsB,mBAAmB;EAAE,GAChCnB,IAAI,GACPQ,eAAe,gBACd7B,KAAA,CAAAmC,aAAA,CAAC5B,WAAW;IAACW,KAAK,EAAEmB,MAAM,CAACE;EAAU,gBACnCvC,KAAA,CAAAmC,aAAA,CAAC7B,MAAM;IACL2C,OAAO;IACPC,aAAa;IACbrC,aAAa,EAAEA,aAAc;IAC7BC,YAAY,EAAEA,YAAa;IAC3BqC,cAAc,EAAEvC,YAAY,EAAEwC,UAAU,IAAI,KAAM;IAClDlC,KAAK,EAAEhB,UAAU,CAACmD;EAAa,GAC9BnB,OACK,CACG,CAAC,GAEdA,OAEI,CAAC;AAEb;AAEA,4BAAelC,KAAK,CAACsD,UAAU,CAAC5C,eAAe,CAAC;AAEhD,MAAM2B,MAAM,GAAGnC,UAAU,CAACqD,MAAM,CAAC;EAC/BhB,SAAS,EAAE;IACTiB,IAAI,EAAE;EACR,CAAC;EACDlB,QAAQ,EAAE;IACRmB,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,CAAC;IACRC,GAAG,EAAE;EACP;AACF,CAAC,CAAC", "ignoreList": []}