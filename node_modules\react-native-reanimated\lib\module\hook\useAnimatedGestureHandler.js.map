{"version": 3, "names": ["useEvent", "useHandler", "EVENT_TYPE", "UNDETERMINED", "FAILED", "BEGAN", "CANCELLED", "ACTIVE", "END", "useAnimatedGestureHandler", "handlers", "dependencies", "context", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useWeb", "handler", "e", "event", "nativeEvent", "state", "onStart", "onActive", "oldState", "onEnd", "onFail", "onCancel", "onFinish"], "sourceRoot": "../../../src", "sources": ["hook/useAnimatedGestureHandler.ts"], "mappings": "AAAA,YAAY;;AAMZ,SAASA,QAAQ,QAAQ,eAAY;AACrC,SAASC,UAAU,QAAQ,iBAAc;AAEzC,MAAMC,UAAU,GAAG;EACjBC,YAAY,EAAE,CAAC;EACfC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,CAAC;EACRC,SAAS,EAAE,CAAC;EACZC,MAAM,EAAE,CAAC;EACTC,GAAG,EAAE;AACP,CAAU;;AAIV;AACA;;AAkDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,yBAAyBA,CAIvCC,QAAyC,EAAEC,YAA6B,EAAE;EAG1E,MAAM;IAAEC,OAAO;IAAEC,oBAAoB;IAAEC;EAAO,CAAC,GAAGb,UAAU,CAC1DS,QAAQ,EACRC,YACF,CAAC;EACD,MAAMI,OAAO,GAAIC,CAAmB,IAAK;IACvC,SAAS;;IACT,MAAMC,KAAK,GAAGH,MAAM;IAChB;IACA;IACA;IACEE,CAAC,CAAWE,WAAW,GACxBF,CAA4B;IAEjC,IAAIC,KAAK,CAACE,KAAK,KAAKjB,UAAU,CAACG,KAAK,IAAIK,QAAQ,CAACU,OAAO,EAAE;MACxDV,QAAQ,CAACU,OAAO,CAACH,KAAK,EAAEL,OAAO,CAAC;IAClC;IACA,IAAIK,KAAK,CAACE,KAAK,KAAKjB,UAAU,CAACK,MAAM,IAAIG,QAAQ,CAACW,QAAQ,EAAE;MAC1DX,QAAQ,CAACW,QAAQ,CAACJ,KAAK,EAAEL,OAAO,CAAC;IACnC;IACA,IACEK,KAAK,CAACK,QAAQ,KAAKpB,UAAU,CAACK,MAAM,IACpCU,KAAK,CAACE,KAAK,KAAKjB,UAAU,CAACM,GAAG,IAC9BE,QAAQ,CAACa,KAAK,EACd;MACAb,QAAQ,CAACa,KAAK,CAACN,KAAK,EAAEL,OAAO,CAAC;IAChC;IACA,IACEK,KAAK,CAACK,QAAQ,KAAKpB,UAAU,CAACG,KAAK,IACnCY,KAAK,CAACE,KAAK,KAAKjB,UAAU,CAACE,MAAM,IACjCM,QAAQ,CAACc,MAAM,EACf;MACAd,QAAQ,CAACc,MAAM,CAACP,KAAK,EAAEL,OAAO,CAAC;IACjC;IACA,IACEK,KAAK,CAACK,QAAQ,KAAKpB,UAAU,CAACK,MAAM,IACpCU,KAAK,CAACE,KAAK,KAAKjB,UAAU,CAACI,SAAS,IACpCI,QAAQ,CAACe,QAAQ,EACjB;MACAf,QAAQ,CAACe,QAAQ,CAACR,KAAK,EAAEL,OAAO,CAAC;IACnC;IACA,IACE,CAACK,KAAK,CAACK,QAAQ,KAAKpB,UAAU,CAACG,KAAK,IAClCY,KAAK,CAACK,QAAQ,KAAKpB,UAAU,CAACK,MAAM,KACtCU,KAAK,CAACE,KAAK,KAAKjB,UAAU,CAACG,KAAK,IAChCY,KAAK,CAACE,KAAK,KAAKjB,UAAU,CAACK,MAAM,IACjCG,QAAQ,CAACgB,QAAQ,EACjB;MACAhB,QAAQ,CAACgB,QAAQ,CACfT,KAAK,EACLL,OAAO,EACPK,KAAK,CAACE,KAAK,KAAKjB,UAAU,CAACI,SAAS,IAClCW,KAAK,CAACE,KAAK,KAAKjB,UAAU,CAACE,MAC/B,CAAC;IACH;EACF,CAAC;EAED,IAAIU,MAAM,EAAE;IACV,OAAOC,OAAO;EAChB;;EAEA;EACA,OAAOf,QAAQ,CACbe,OAAO,EACP,CAAC,6BAA6B,EAAE,uBAAuB,CAAC,EACxDF;EACA;EACF,CAAC;AACH", "ignoreList": []}