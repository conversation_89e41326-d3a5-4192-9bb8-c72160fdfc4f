{"version": 3, "names": ["ComplexAnimationBuilder", "RollInLeft", "presetName", "createInstance", "build", "delayFunction", "getDelayFunction", "animation", "config", "getAnimationAndConfig", "delay", "get<PERSON>elay", "callback", "callbackV", "initialValues", "values", "animations", "transform", "translateX", "rotate", "windowWidth", "RollInRight", "RollOutLeft", "RollOutRight"], "sourceRoot": "../../../../src", "sources": ["layoutReanimation/defaultAnimations/Roll.ts"], "mappings": "AAAA,YAAY;;AAOZ,SAASA,uBAAuB,QAAQ,8BAAqB;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,SACbD,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,YAAY;EAEhC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIF,UAAU,CAAC,CAAC;EACzB;EAEAG,KAAK,GAAGA,CAAA,KAAkC;IACxC,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAQC,MAAiC,IAAK;MAC5C,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVC,SAAS,EAAE,CACT;YAAEC,UAAU,EAAEb,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;UAAE,CAAC,EAC1D;YAAEW,MAAM,EAAEd,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,MAAM,EAAEC,MAAM,CAAC;UAAE,CAAC;QAE/D,CAAC;QACDM,aAAa,EAAE;UACbG,SAAS,EAAE,CACT;YAAEC,UAAU,EAAE,CAACH,MAAM,CAACK;UAAY,CAAC,EACnC;YAAED,MAAM,EAAE;UAAU,CAAC,CACtB;UACD,GAAGL;QACL,CAAC;QACDF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMS,WAAW,SACdrB,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,aAAa;EAEjC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIkB,WAAW,CAAC,CAAC;EAC1B;EAEAjB,KAAK,GAAGA,CAAA,KAAkC;IACxC,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAQC,MAAiC,IAAK;MAC5C,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVC,SAAS,EAAE,CACT;YAAEC,UAAU,EAAEb,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,CAAC,EAAEC,MAAM,CAAC;UAAE,CAAC,EAC1D;YAAEW,MAAM,EAAEd,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,MAAM,EAAEC,MAAM,CAAC;UAAE,CAAC;QAE/D,CAAC;QACDM,aAAa,EAAE;UACbG,SAAS,EAAE,CAAC;YAAEC,UAAU,EAAEH,MAAM,CAACK;UAAY,CAAC,EAAE;YAAED,MAAM,EAAE;UAAS,CAAC,CAAC;UACrE,GAAGL;QACL,CAAC;QACDF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMU,WAAW,SACdtB,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,aAAa;EAEjC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAImB,WAAW,CAAC,CAAC;EAC1B;EAEAlB,KAAK,GAAGA,CAAA,KAAkC;IACxC,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAQC,MAAiC,IAAK;MAC5C,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVC,SAAS,EAAE,CACT;YACEC,UAAU,EAAEb,aAAa,CACvBK,KAAK,EACLH,SAAS,CAAC,CAACQ,MAAM,CAACK,WAAW,EAAEZ,MAAM,CACvC;UACF,CAAC,EACD;YAAEW,MAAM,EAAEd,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,SAAS,EAAEC,MAAM,CAAC;UAAE,CAAC;QAElE,CAAC;QACDM,aAAa,EAAE;UACbG,SAAS,EAAE,CAAC;YAAEC,UAAU,EAAE;UAAE,CAAC,EAAE;YAAEC,MAAM,EAAE;UAAO,CAAC,CAAC;UAClD,GAAGL;QACL,CAAC;QACDF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMW,YAAY,SACfvB,uBAAuB,CAEjC;EACE,OAAOE,UAAU,GAAG,cAAc;EAElC,OAAOC,cAAcA,CAAA,EAEF;IACjB,OAAO,IAAIoB,YAAY,CAAC,CAAC;EAC3B;EAEAnB,KAAK,GAAGA,CAAA,KAAkC;IACxC,MAAMC,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IAC7C,MAAM,CAACC,SAAS,EAAEC,MAAM,CAAC,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACxD,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC7B,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACA,aAAa;IAExC,OAAQC,MAAiC,IAAK;MAC5C,SAAS;;MACT,OAAO;QACLC,UAAU,EAAE;UACVC,SAAS,EAAE,CACT;YACEC,UAAU,EAAEb,aAAa,CACvBK,KAAK,EACLH,SAAS,CAACQ,MAAM,CAACK,WAAW,EAAEZ,MAAM,CACtC;UACF,CAAC,EACD;YAAEW,MAAM,EAAEd,aAAa,CAACK,KAAK,EAAEH,SAAS,CAAC,QAAQ,EAAEC,MAAM,CAAC;UAAE,CAAC;QAEjE,CAAC;QACDM,aAAa,EAAE;UACbG,SAAS,EAAE,CAAC;YAAEC,UAAU,EAAE;UAAE,CAAC,EAAE;YAAEC,MAAM,EAAE;UAAO,CAAC,CAAC;UAClD,GAAGL;QACL,CAAC;QACDF;MACF,CAAC;IACH,CAAC;EACH,CAAC;AACH", "ignoreList": []}