{"version": 3, "file": "race.js", "sources": ["../../../src/internal/observable/race.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAC;AAC1C,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAKxC,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAErD,OAAO,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAoD9D,MAAM,UAAU,IAAI,CAAI,GAAG,WAAmC;IAG5D,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;QAC5B,IAAI,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;YAC3B,WAAW,GAAG,WAAW,CAAC,CAAC,CAAsB,CAAC;SACnD;aAAM;YACL,OAAO,WAAW,CAAC,CAAC,CAAkB,CAAC;SACxC;KACF;IAED,OAAO,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,YAAY,EAAK,CAAC,CAAC;AACvE,CAAC;AAED,MAAM,OAAO,YAAY;IACvB,IAAI,CAAC,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC;IAC1D,CAAC;CACF;AAOD,MAAM,OAAO,cAAkB,SAAQ,eAAqB;IAK1D,YAAY,WAA0B;QACpC,KAAK,CAAC,WAAW,CAAC,CAAC;QALb,aAAQ,GAAY,KAAK,CAAC;QAC1B,gBAAW,GAAsB,EAAE,CAAC;QACpC,kBAAa,GAAmB,EAAE,CAAC;IAI3C,CAAC;IAES,KAAK,CAAC,UAAe;QAC7B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACpC,CAAC;IAES,SAAS;QACjB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,MAAM,GAAG,GAAG,WAAW,CAAC,MAAM,CAAC;QAE/B,IAAI,GAAG,KAAK,CAAC,EAAE;YACb,IAAI,CAAC,WAAW,CAAC,QAAS,EAAE,CAAC;SAC9B;aAAM;YACL,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE;gBAC9C,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;gBAClC,MAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC,CAAE,CAAC;gBAExE,IAAI,IAAI,CAAC,aAAa,EAAE;oBACtB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;iBACvC;gBACD,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;aACxB;YACD,IAAI,CAAC,WAAW,GAAG,IAAK,CAAC;SAC1B;IACH,CAAC;IAED,UAAU,CAAC,WAAc,EAAE,UAAa,EAC7B,UAAkB;QAC3B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YAErB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAClD,IAAI,CAAC,KAAK,UAAU,EAAE;oBACpB,IAAI,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;oBAEzC,YAAY,CAAC,WAAW,EAAE,CAAC;oBAC3B,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;iBAC3B;aACF;YAED,IAAI,CAAC,aAAa,GAAG,IAAK,CAAC;SAC5B;QAED,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,UAAU,CAAC,CAAC;IACrC,CAAC;CACF"}