{"version": 3, "names": ["processColorsInProps", "logger", "isChromeDebugger", "isF<PERSON><PERSON>", "isJest", "shouldBeUseWeb", "setNativeProps", "setNativePropsFabric", "animatedRef", "updates", "_WORKLET", "warn", "shadowNodeWrapper", "global", "_updatePropsFabric", "setNativePropsPaper", "tag", "name", "viewName", "value", "_updatePropsPaper", "setNativePropsJest", "setNativePropsChromeDebugger", "setNativePropsDefault"], "sourceRoot": "../../../src", "sources": ["platformFunctions/setNativeProps.ts"], "mappings": "AAAA,YAAY;;AAGZ,SAASA,oBAAoB,QAAQ,cAAW;AAOhD,SAASC,MAAM,QAAQ,oBAAW;AAClC,SACEC,gBAAgB,EAChBC,QAAQ,EACRC,MAAM,EACNC,cAAc,QACT,uBAAoB;AAM3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,cAA8B;AAEzC,SAASC,oBAAoBA,CAC3BC,WAA8C,EAC9CC,OAAmB,EACnB;EACA,SAAS;;EACT,IAAI,CAACC,QAAQ,EAAE;IACbT,MAAM,CAACU,IAAI,CAAC,sDAAsD,CAAC;IACnE;EACF;EACA,MAAMC,iBAAiB,GAAGJ,WAAW,CAAC,CAAsB;EAC5DR,oBAAoB,CAACS,OAAO,CAAC;EAC7BI,MAAM,CAACC,kBAAkB,CAAE,CAAC;IAAEF,iBAAiB;IAAEH;EAAQ,CAAC,CAAC,CAAC;AAC9D;AAEA,SAASM,mBAAmBA,CAC1BP,WAA8C,EAC9CC,OAAmB,EACnB;EACA,SAAS;;EACT,IAAI,CAACC,QAAQ,EAAE;IACbT,MAAM,CAACU,IAAI,CAAC,sDAAsD,CAAC;IACnE;EACF;EACA,MAAMK,GAAG,GAAGR,WAAW,CAAC,CAAW;EACnC,MAAMS,IAAI,GAAIT,WAAW,CAAqBU,QAAQ,CAACC,KAAK;EAC5DnB,oBAAoB,CAACS,OAAO,CAAC;EAC7BI,MAAM,CAACO,iBAAiB,CAAE,CAAC;IAAEJ,GAAG;IAAEC,IAAI;IAAER;EAAQ,CAAC,CAAC,CAAC;AACrD;AAEA,SAASY,kBAAkBA,CAAA,EAAG;EAC5BpB,MAAM,CAACU,IAAI,CAAC,8CAA8C,CAAC;AAC7D;AAEA,SAASW,4BAA4BA,CAAA,EAAG;EACtCrB,MAAM,CAACU,IAAI,CAAC,yDAAyD,CAAC;AACxE;AAEA,SAASY,qBAAqBA,CAAA,EAAG;EAC/BtB,MAAM,CAACU,IAAI,CAAC,0DAA0D,CAAC;AACzE;AAEA,IAAI,CAACN,cAAc,CAAC,CAAC,EAAE;EACrB;EACA;EACA;EACA,IAAIF,QAAQ,CAAC,CAAC,EAAE;IACdG,cAAc,GAAGC,oBAAiD;EACpE,CAAC,MAAM;IACLD,cAAc,GAAGS,mBAAgD;EACnE;AACF,CAAC,MAAM,IAAIX,MAAM,CAAC,CAAC,EAAE;EACnBE,cAAc,GAAGe,kBAAkB;AACrC,CAAC,MAAM,IAAInB,gBAAgB,CAAC,CAAC,EAAE;EAC7BI,cAAc,GAAGgB,4BAA4B;AAC/C,CAAC,MAAM;EACLhB,cAAc,GAAGiB,qBAAqB;AACxC", "ignoreList": []}